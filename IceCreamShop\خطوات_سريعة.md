# خطوات التثبيت السريعة - نظام إدارة محل الآيس كريم

## 🚀 للمستخدمين المتعجلين

### 1️⃣ تثبيت Node.js (5 دقائق)
```
• اذهب إلى: https://nodejs.org/ar
• انقر "تنزيل" للنسخة LTS
• شغل ملف التثبيت واتبع التعليمات
• أعد تشغيل الكمبيوتر
```

### 2️⃣ تنزيل التطبيق (2 دقيقة)
```
• نزل ملف التطبيق المضغوط
• فك الضغط في مجلد جديد
• مثال: C:\IceCreamShop
```

### 3️⃣ تثبيت المكتبات (3 دقائق)
```
• افتح موجه الأوامر في مجلد التطبيق
• اكتب: npm install
• انتظر حتى اكتمال التثبيت
```

### 4️⃣ تشغيل التطبيق (30 ثانية)
```
Windows: انقر مرتين على start-desktop.bat
Mac: انقر مرتين على start-desktop.command  
Linux: شغل ./start-desktop.sh
```

### 5️⃣ تسجيل الدخول
```
اسم المستخدم: admin
كلمة المرور: admin123
```

## ⚡ إجمالي وقت التثبيت: 10 دقائق

---

## 🔧 حل سريع للمشاكل

| المشكلة | الحل السريع |
|---------|------------|
| Node.js غير موجود | تأكد من التثبيت وأعد تشغيل الكمبيوتر |
| فشل npm install | شغل موجه الأوامر كمدير |
| المنفذ 3000 مشغول | أعد تشغيل الكمبيوتر |
| صفحة فارغة | امسح ذاكرة المتصفح المؤقت |

---

## 📱 روابط مهمة
- **التطبيق المحلي**: http://localhost:3000
- **من أجهزة أخرى**: http://[عنوان_IP]:3000
- **تغيير كلمة المرور**: إعدادات > إدارة المستخدمين