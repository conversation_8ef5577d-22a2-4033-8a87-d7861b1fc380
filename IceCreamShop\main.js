const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const express = require('express');
const { spawn } = require('child_process');

// Express server instance
let server;
let serverProcess;
const PORT = 3000;

// Create the main window
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.ico'), // We'll create this
    title: 'نظام إدارة محل الآيس كريم - Golden Ice Cream Shop',
    show: false,
    titleBarStyle: 'default'
  });

  // Create Arabic menu
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Clear all data
            mainWindow.webContents.send('clear-data');
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'JSON Files', extensions: ['json'] }
              ]
            });
            
            if (!result.canceled) {
              mainWindow.webContents.send('import-data', result.filePaths[0]);
            }
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('save-data');
          }
        },
        {
          label: 'حفظ باسم',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: async () => {
            const result = await dialog.showSaveDialog(mainWindow, {
              filters: [
                { name: 'JSON Files', extensions: ['json'] }
              ],
              defaultPath: `icecreamshop_backup_${new Date().toISOString().split('T')[0]}.json`
            });
            
            if (!result.canceled) {
              mainWindow.webContents.send('export-data', result.filePath);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        {
          label: 'تراجع',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo'
        },
        {
          label: 'إعادة',
          accelerator: 'CmdOrCtrl+Y',
          role: 'redo'
        },
        { type: 'separator' },
        {
          label: 'قص',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut'
        },
        {
          label: 'نسخ',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: 'لصق',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'إعادة تحميل',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: 'تكبير',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
          }
        },
        {
          label: 'تصغير',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
          }
        },
        {
          label: 'حجم طبيعي',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            mainWindow.webContents.setZoomLevel(0);
          }
        },
        { type: 'separator' },
        {
          label: 'ملء الشاشة',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Command+F' : 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        },
        {
          label: 'أدوات المطور',
          accelerator: 'F12',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        {
          label: 'تصغير',
          accelerator: 'CmdOrCtrl+M',
          role: 'minimize'
        },
        {
          label: 'إغلاق',
          accelerator: 'CmdOrCtrl+W',
          role: 'close'
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول البرنامج',
              message: 'نظام إدارة محل الآيس كريم',
              detail: 'نسخة 1.0.0\nنظام شامل لإدارة محلات الآيس كريم\nيدعم اللغة العربية والريال العماني'
            });
          }
        },
        {
          label: 'دليل المستخدم',
          click: () => {
            // Open user manual
            shell.openExternal('https://github.com');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // Start the express server
  startServer().then(() => {
    // Load the application
    mainWindow.loadURL(`http://localhost:${PORT}`);
    
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      
      // Focus on the window
      if (process.platform === 'darwin') {
        app.dock.show();
      }
    });
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    stopServer();
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}

// Start Express server
async function startServer() {
  return new Promise((resolve, reject) => {
    const expressApp = express();
    
    // Middleware
    expressApp.use(express.json({ limit: '50mb' }));
    expressApp.use(express.static('.'));
    
    // Import server routes
    const serverRoutes = require('./server-routes');
    expressApp.use('/api', serverRoutes);
    
    // Serve main application
    expressApp.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'index.html'));
    });
    
    // Start server
    server = expressApp.listen(PORT, 'localhost', () => {
      console.log(`🍦 Desktop server running on port ${PORT}`);
      resolve();
    });
    
    server.on('error', (err) => {
      console.error('Server error:', err);
      reject(err);
    });
  });
}

// Stop Express server
function stopServer() {
  if (server) {
    server.close();
    server = null;
  }
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  stopServer();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  stopServer();
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(options);
  return result;
});

ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Set app details
app.setName('نظام إدارة محل الآيس كريم');
app.setAboutPanelOptions({
  applicationName: 'نظام إدارة محل الآيس كريم',
  applicationVersion: '1.0.0',
  copyright: 'جميع الحقوق محفوظة',
  credits: 'نظام شامل لإدارة محلات الآيس كريم'
});