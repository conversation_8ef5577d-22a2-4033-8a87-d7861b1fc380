{"name": "workspace", "version": "1.0.0", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@neondatabase/serverless": "^1.0.1", "@types/node": "^24.0.12", "@types/ws": "^8.18.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "electron": "^37.2.0", "electron-builder": "^26.0.12", "express": "^5.1.0", "nexe": "^5.0.0-beta.4", "pg": "^8.16.3", "pkg": "^5.8.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "ws": "^8.18.3"}, "description": "نظام شامل لإدارة محلات الآيس كريم باللغة العربية مع دعم الريال العماني"}