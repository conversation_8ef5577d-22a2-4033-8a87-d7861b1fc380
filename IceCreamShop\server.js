const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const app = express();

// Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.static('.'));

// CORS middleware
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Data storage file
const DATA_FILE = 'icecreamshop_data.json';

// Helper function to read data
async function readData() {
    try {
        const data = await fs.readFile(DATA_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        // Return default data if file doesn't exist
        return {
            products: [],
            sales: [],
            customers: [],
            users: [
                {
                    id: 1,
                    username: 'admin',
                    password: 'admin123',
                    name: 'المدير',
                    role: 'admin',
                    permissions: ['all'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            ],
            settings: {
                shopName: 'محل الآيس كريم الذهبي',
                shopNameEn: 'Golden Ice Cream Shop',
                currency: 'OMR',
                currencySymbol: 'ر.ع',
                taxRate: 0.05,
                receiptHeader: 'محل الآيس كريم الذهبي',
                receiptFooter: 'شكراً لزيارتكم',
                lowStockAlert: true,
                autoBackup: true,
                language: 'ar'
            }
        };
    }
}

// Helper function to write data
async function writeData(data) {
    await fs.writeFile(DATA_FILE, JSON.stringify(data, null, 2));
}

// Routes

// Get all data
app.get('/api/data', async (req, res) => {
    try {
        const data = await readData();
        res.json(data);
    } catch (error) {
        console.error('Error reading data:', error);
        res.status(500).json({ error: 'فشل في قراءة البيانات' });
    }
});

// Save all data
app.post('/api/data', async (req, res) => {
    try {
        await writeData(req.body);
        res.json({ message: 'تم حفظ البيانات بنجاح' });
    } catch (error) {
        console.error('Error saving data:', error);
        res.status(500).json({ error: 'فشل في حفظ البيانات' });
    }
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        const data = await readData();
        
        const user = data.users.find(u => u.username === username && u.password === password);
        
        if (user && user.isActive) {
            // Update last login
            user.lastLogin = new Date().toISOString();
            await writeData(data);
            
            // Return user without password
            const { password: _, ...userWithoutPassword } = user;
            res.json({ 
                success: true, 
                user: userWithoutPassword,
                message: 'تم تسجيل الدخول بنجاح'
            });
        } else {
            res.status(401).json({ 
                success: false, 
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة' 
            });
        }
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'خطأ في الخادم' });
    }
});

// Products endpoints
app.get('/api/products', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.products.filter(p => p.isActive));
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المنتجات' });
    }
});

app.post('/api/products', async (req, res) => {
    try {
        const data = await readData();
        const newProduct = {
            id: Math.max(...data.products.map(p => p.id), 0) + 1,
            ...req.body,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.products.push(newProduct);
        await writeData(data);
        res.json(newProduct);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة المنتج' });
    }
});

app.put('/api/products/:id', async (req, res) => {
    try {
        const data = await readData();
        const productIndex = data.products.findIndex(p => p.id === parseInt(req.params.id));
        
        if (productIndex === -1) {
            return res.status(404).json({ error: 'المنتج غير موجود' });
        }
        
        data.products[productIndex] = {
            ...data.products[productIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        res.json(data.products[productIndex]);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث المنتج' });
    }
});

app.delete('/api/products/:id', async (req, res) => {
    try {
        const data = await readData();
        const productIndex = data.products.findIndex(p => p.id === parseInt(req.params.id));
        
        if (productIndex === -1) {
            return res.status(404).json({ error: 'المنتج غير موجود' });
        }
        
        // Soft delete
        data.products[productIndex].isActive = false;
        data.products[productIndex].updatedAt = new Date().toISOString();
        
        await writeData(data);
        res.json({ message: 'تم حذف المنتج بنجاح' });
    } catch (error) {
        res.status(500).json({ error: 'فشل في حذف المنتج' });
    }
});

// Sales endpoints
app.get('/api/sales', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.sales);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المبيعات' });
    }
});

app.post('/api/sales', async (req, res) => {
    try {
        const data = await readData();
        const newSale = {
            id: Math.max(...data.sales.map(s => s.id), 0) + 1,
            transactionId: `TXN${String(data.sales.length + 1).padStart(6, '0')}`,
            date: new Date().toISOString(),
            ...req.body,
            status: 'completed',
            createdAt: new Date().toISOString()
        };
        
        // Update inventory
        req.body.items.forEach(item => {
            const product = data.products.find(p => p.id === item.productId);
            if (product) {
                product.stock -= item.quantity;
                product.updatedAt = new Date().toISOString();
            }
        });
        
        data.sales.push(newSale);
        await writeData(data);
        res.json(newSale);
    } catch (error) {
        res.status(500).json({ error: 'فشل في حفظ المبيعات' });
    }
});

// Customers endpoints
app.get('/api/customers', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.customers);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة العملاء' });
    }
});

app.post('/api/customers', async (req, res) => {
    try {
        const data = await readData();
        const newCustomer = {
            id: Math.max(...data.customers.map(c => c.id), 0) + 1,
            ...req.body,
            totalPurchases: 0,
            visits: 0,
            createdAt: new Date().toISOString()
        };
        
        data.customers.push(newCustomer);
        await writeData(data);
        res.json(newCustomer);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة العميل' });
    }
});

app.put('/api/customers/:id', async (req, res) => {
    try {
        const data = await readData();
        const customerIndex = data.customers.findIndex(c => c.id === parseInt(req.params.id));
        
        if (customerIndex === -1) {
            return res.status(404).json({ error: 'العميل غير موجود' });
        }
        
        data.customers[customerIndex] = {
            ...data.customers[customerIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        res.json(data.customers[customerIndex]);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث العميل' });
    }
});

// Users endpoints
app.get('/api/users', async (req, res) => {
    try {
        const data = await readData();
        // Return users without passwords
        const usersWithoutPasswords = data.users.map(({ password, ...user }) => user);
        res.json(usersWithoutPasswords);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المستخدمين' });
    }
});

app.post('/api/users', async (req, res) => {
    try {
        const data = await readData();
        
        // Check if username already exists
        if (data.users.find(u => u.username === req.body.username)) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }
        
        const newUser = {
            id: Math.max(...data.users.map(u => u.id), 0) + 1,
            ...req.body,
            isActive: true,
            createdAt: new Date().toISOString(),
            lastLogin: null
        };
        
        data.users.push(newUser);
        await writeData(data);
        
        // Return user without password
        const { password: _, ...userWithoutPassword } = newUser;
        res.json(userWithoutPassword);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة المستخدم' });
    }
});

app.put('/api/users/:id', async (req, res) => {
    try {
        const data = await readData();
        const userIndex = data.users.findIndex(u => u.id === parseInt(req.params.id));
        
        if (userIndex === -1) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        data.users[userIndex] = {
            ...data.users[userIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        
        // Return user without password
        const { password: _, ...userWithoutPassword } = data.users[userIndex];
        res.json(userWithoutPassword);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث المستخدم' });
    }
});

// Settings endpoints
app.get('/api/settings', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.settings);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة الإعدادات' });
    }
});

app.put('/api/settings', async (req, res) => {
    try {
        const data = await readData();
        data.settings = { ...data.settings, ...req.body };
        await writeData(data);
        res.json(data.settings);
    } catch (error) {
        res.status(500).json({ error: 'فشل في حفظ الإعدادات' });
    }
});

// Backup and export endpoints
app.get('/api/export', async (req, res) => {
    try {
        const data = await readData();
        const exportData = {
            ...data,
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename=icecreamshop_backup_${new Date().toISOString().split('T')[0]}.json`);
        res.json(exportData);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تصدير البيانات' });
    }
});

app.post('/api/import', async (req, res) => {
    try {
        // Validate import data structure
        const importData = req.body;
        if (!importData.products || !importData.sales || !importData.customers || !importData.users || !importData.settings) {
            return res.status(400).json({ error: 'بيانات الاستيراد غير صالحة' });
        }
        
        await writeData(importData);
        res.json({ message: 'تم استيراد البيانات بنجاح' });
    } catch (error) {
        res.status(500).json({ error: 'فشل في استيراد البيانات' });
    }
});

// Analytics endpoints
app.get('/api/analytics/dashboard', async (req, res) => {
    try {
        const data = await readData();
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        
        const todaySales = data.sales.filter(sale => new Date(sale.date) >= startOfDay);
        const monthSales = data.sales.filter(sale => new Date(sale.date) >= startOfMonth);
        
        const stats = {
            todaySales: todaySales.length,
            todayRevenue: todaySales.reduce((sum, sale) => sum + sale.total, 0),
            monthSales: monthSales.length,
            monthRevenue: monthSales.reduce((sum, sale) => sum + sale.total, 0),
            totalProducts: data.products.filter(p => p.isActive).length,
            totalCustomers: data.customers.length,
            lowStockProducts: data.products.filter(p => p.stock <= p.minStock).length,
            totalRevenue: data.sales.reduce((sum, sale) => sum + sale.total, 0)
        };
        
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة الإحصائيات' });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'خادم نظام إدارة محل الآيس كريم يعمل بنجاح',
        timestamp: new Date().toISOString()
    });
});

// Serve main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({ error: 'خطأ في الخادم' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'المسار غير موجود' });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🍦 خادم نظام إدارة محل الآيس كريم يعمل على المنفذ ${PORT}`);
    console.log(`🌐 يمكن الوصول للتطبيق على: http://localhost:${PORT}`);
    console.log(`📊 واجهة برمجة التطبيقات متاحة على: http://localhost:${PORT}/api`);
});
