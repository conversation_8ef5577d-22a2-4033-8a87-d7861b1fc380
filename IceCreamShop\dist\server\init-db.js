"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
const db_1 = require("./db");
const schema_1 = require("../shared/schema");
async function initializeDatabase() {
    console.log('🔄 Initializing database with default data...');
    try {
        // Check if admin user exists
        const existingUsers = await db_1.db.select().from(schema_1.users).limit(1);
        if (existingUsers.length > 0) {
            console.log('✅ Database already initialized');
            return;
        }
        // Create default admin user
        const defaultUser = {
            username: 'admin',
            password: 'admin123',
            name: 'المدير',
            role: 'admin',
            permissions: ['all'],
            isActive: true
        };
        await db_1.db.insert(schema_1.users).values(defaultUser);
        console.log('✅ Default admin user created');
        // Create default products
        const defaultProducts = [
            {
                name: 'آيس كريم الفانيليا',
                category: 'آيس كريم',
                price: '2.500',
                cost: '1.200',
                stock: 50,
                minStock: 10,
                description: 'آيس كريم فانيليا طبيعي',
                isActive: true
            },
            {
                name: 'آيس كريم الشوكولاتة',
                category: 'آيس كريم',
                price: '3.000',
                cost: '1.500',
                stock: 40,
                minStock: 10,
                description: 'آيس كريم شوكولاتة بلجيكي',
                isActive: true
            },
            {
                name: 'آيس كريم الفراولة',
                category: 'آيس كريم',
                price: '2.800',
                cost: '1.300',
                stock: 35,
                minStock: 10,
                description: 'آيس كريم فراولة طبيعية',
                isActive: true
            },
            {
                name: 'آيس كريم المانجو',
                category: 'آيس كريم',
                price: '3.200',
                cost: '1.600',
                stock: 30,
                minStock: 10,
                description: 'آيس كريم مانجو استوائي',
                isActive: true
            },
            {
                name: 'آيس كريم الفستق',
                category: 'آيس كريم',
                price: '3.500',
                cost: '1.800',
                stock: 25,
                minStock: 10,
                description: 'آيس كريم فستق حلبي',
                isActive: true
            }
        ];
        await db_1.db.insert(schema_1.products).values(defaultProducts);
        console.log('✅ Default products created');
        // Create default customer
        const defaultCustomer = {
            name: 'أحمد محمد',
            phone: '96899123456',
            email: '<EMAIL>',
            address: 'مسقط، سلطنة عمان',
            membershipType: 'ذهبي',
            discount: 10,
            totalPurchases: '0',
            visits: 0
        };
        await db_1.db.insert(schema_1.customers).values(defaultCustomer);
        console.log('✅ Default customer created');
        // Create default settings
        const defaultSettings = [
            { key: 'shopName', value: 'محل الآيس كريم الذهبي', type: 'string' },
            { key: 'shopNameEn', value: 'Golden Ice Cream Shop', type: 'string' },
            { key: 'shopAddress', value: 'مسقط، سلطنة عمان', type: 'string' },
            { key: 'shopPhone', value: '96824123456', type: 'string' },
            { key: 'shopEmail', value: '<EMAIL>', type: 'string' },
            { key: 'currency', value: 'OMR', type: 'string' },
            { key: 'currencySymbol', value: 'ر.ع', type: 'string' },
            { key: 'taxRate', value: '0.05', type: 'number' },
            { key: 'receiptHeader', value: 'محل الآيس كريم الذهبي', type: 'string' },
            { key: 'receiptFooter', value: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى', type: 'string' },
            { key: 'receiptFontSize', value: '14', type: 'number' },
            { key: 'lowStockAlert', value: 'true', type: 'boolean' },
            { key: 'autoBackup', value: 'true', type: 'boolean' },
            { key: 'soundNotifications', value: 'true', type: 'boolean' },
            { key: 'emailReports', value: 'false', type: 'boolean' },
            { key: 'language', value: 'ar', type: 'string' }
        ];
        await db_1.db.insert(schema_1.settings).values(defaultSettings);
        console.log('✅ Default settings created');
        console.log('🎉 Database initialization completed successfully!');
    }
    catch (error) {
        console.error('❌ Database initialization failed:', error);
        throw error;
    }
}
// Run initialization if this file is executed directly
if (require.main === module) {
    initializeDatabase()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
}
