"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.storage = exports.DatabaseStorage = void 0;
const schema_1 = require("../shared/schema");
const db_1 = require("./db");
const drizzle_orm_1 = require("drizzle-orm");
// Database storage implementation
class DatabaseStorage {
    async getUser(id) {
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.id, id));
        return user || undefined;
    }
    async getUserByUsername(username) {
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.username, username));
        return user || undefined;
    }
    async createUser(insertUser) {
        const [user] = await db_1.db
            .insert(schema_1.users)
            .values(insertUser)
            .returning();
        return user;
    }
    async updateUser(id, userData) {
        const [user] = await db_1.db
            .update(schema_1.users)
            .set({ ...userData, updatedAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, id))
            .returning();
        return user;
    }
    async getAllUsers() {
        return await db_1.db.select().from(schema_1.users).orderBy((0, drizzle_orm_1.desc)(schema_1.users.createdAt));
    }
    async getProduct(id) {
        const [product] = await db_1.db.select().from(schema_1.products).where((0, drizzle_orm_1.eq)(schema_1.products.id, id));
        return product || undefined;
    }
    async getAllProducts() {
        return await db_1.db.select().from(schema_1.products).where((0, drizzle_orm_1.eq)(schema_1.products.isActive, true)).orderBy((0, drizzle_orm_1.desc)(schema_1.products.createdAt));
    }
    async createProduct(insertProduct) {
        const [product] = await db_1.db
            .insert(schema_1.products)
            .values(insertProduct)
            .returning();
        return product;
    }
    async updateProduct(id, productData) {
        const [product] = await db_1.db
            .update(schema_1.products)
            .set({ ...productData, updatedAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, id))
            .returning();
        return product;
    }
    async deleteProduct(id) {
        await db_1.db
            .update(schema_1.products)
            .set({ isActive: false, updatedAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.products.id, id));
    }
    async getSale(id) {
        const [sale] = await db_1.db.select().from(schema_1.sales).where((0, drizzle_orm_1.eq)(schema_1.sales.id, id));
        return sale || undefined;
    }
    async getAllSales() {
        return await db_1.db.select().from(schema_1.sales).orderBy((0, drizzle_orm_1.desc)(schema_1.sales.date));
    }
    async getSalesByDateRange(startDate, endDate) {
        return await db_1.db.select()
            .from(schema_1.sales)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.gte)(schema_1.sales.date, startDate), (0, drizzle_orm_1.lte)(schema_1.sales.date, endDate)))
            .orderBy((0, drizzle_orm_1.desc)(schema_1.sales.date));
    }
    async createSale(saleData, items) {
        return await db_1.db.transaction(async (tx) => {
            // Create sale
            const [sale] = await tx
                .insert(schema_1.sales)
                .values(saleData)
                .returning();
            // Create sale items
            const saleItemsWithSaleId = items.map(item => ({
                ...item,
                saleId: sale.id
            }));
            await tx.insert(schema_1.saleItems).values(saleItemsWithSaleId);
            // Update product stock and create inventory transactions
            for (const item of items) {
                // Update product stock
                await tx
                    .update(schema_1.products)
                    .set({
                    stock: (0, drizzle_orm_1.sql) `${schema_1.products.stock} - ${item.quantity}`,
                    updatedAt: new Date()
                })
                    .where((0, drizzle_orm_1.eq)(schema_1.products.id, item.productId));
                // Create inventory transaction
                await tx.insert(schema_1.inventoryTransactions).values({
                    productId: item.productId,
                    userId: saleData.userId,
                    type: 'out',
                    quantity: -item.quantity,
                    reason: 'Sale',
                    reference: sale.transactionId
                });
            }
            // Update customer statistics if customer exists
            if (saleData.customerId) {
                await tx
                    .update(schema_1.customers)
                    .set({
                    totalPurchases: (0, drizzle_orm_1.sql) `${schema_1.customers.totalPurchases} + ${saleData.total}`,
                    visits: (0, drizzle_orm_1.sql) `${schema_1.customers.visits} + 1`,
                    updatedAt: new Date()
                })
                    .where((0, drizzle_orm_1.eq)(schema_1.customers.id, saleData.customerId));
            }
            return sale;
        });
    }
    async getCustomer(id) {
        const [customer] = await db_1.db.select().from(schema_1.customers).where((0, drizzle_orm_1.eq)(schema_1.customers.id, id));
        return customer || undefined;
    }
    async getAllCustomers() {
        return await db_1.db.select().from(schema_1.customers).orderBy((0, drizzle_orm_1.desc)(schema_1.customers.createdAt));
    }
    async createCustomer(insertCustomer) {
        const [customer] = await db_1.db
            .insert(schema_1.customers)
            .values(insertCustomer)
            .returning();
        return customer;
    }
    async updateCustomer(id, customerData) {
        const [customer] = await db_1.db
            .update(schema_1.customers)
            .set({ ...customerData, updatedAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.customers.id, id))
            .returning();
        return customer;
    }
    async getSetting(key) {
        const [setting] = await db_1.db.select().from(schema_1.settings).where((0, drizzle_orm_1.eq)(schema_1.settings.key, key));
        return setting || undefined;
    }
    async getAllSettings() {
        return await db_1.db.select().from(schema_1.settings).orderBy(schema_1.settings.key);
    }
    async setSetting(key, value, type = 'string') {
        const existing = await this.getSetting(key);
        if (existing) {
            const [setting] = await db_1.db
                .update(schema_1.settings)
                .set({ value, type, updatedAt: new Date() })
                .where((0, drizzle_orm_1.eq)(schema_1.settings.key, key))
                .returning();
            return setting;
        }
        else {
            const [setting] = await db_1.db
                .insert(schema_1.settings)
                .values({ key, value, type })
                .returning();
            return setting;
        }
    }
    async createInventoryTransaction(transaction) {
        const [inventoryTransaction] = await db_1.db
            .insert(schema_1.inventoryTransactions)
            .values(transaction)
            .returning();
        return inventoryTransaction;
    }
    async getInventoryTransactions(productId) {
        const query = db_1.db.select().from(schema_1.inventoryTransactions);
        if (productId) {
            return await query.where((0, drizzle_orm_1.eq)(schema_1.inventoryTransactions.productId, productId))
                .orderBy((0, drizzle_orm_1.desc)(schema_1.inventoryTransactions.createdAt));
        }
        return await query.orderBy((0, drizzle_orm_1.desc)(schema_1.inventoryTransactions.createdAt));
    }
    async getDashboardStats() {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const [todaySalesResult] = await db_1.db
            .select({
            count: (0, drizzle_orm_1.sql) `count(*)::int`,
            total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.total})::numeric`
        })
            .from(schema_1.sales)
            .where((0, drizzle_orm_1.gte)(schema_1.sales.date, startOfDay));
        const [monthSalesResult] = await db_1.db
            .select({
            count: (0, drizzle_orm_1.sql) `count(*)::int`,
            total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.total})::numeric`
        })
            .from(schema_1.sales)
            .where((0, drizzle_orm_1.gte)(schema_1.sales.date, startOfMonth));
        const [totalProductsResult] = await db_1.db
            .select({ count: (0, drizzle_orm_1.sql) `count(*)::int` })
            .from(schema_1.products)
            .where((0, drizzle_orm_1.eq)(schema_1.products.isActive, true));
        const [totalCustomersResult] = await db_1.db
            .select({ count: (0, drizzle_orm_1.sql) `count(*)::int` })
            .from(schema_1.customers);
        const [lowStockProductsResult] = await db_1.db
            .select({ count: (0, drizzle_orm_1.sql) `count(*)::int` })
            .from(schema_1.products)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.products.isActive, true), (0, drizzle_orm_1.sql) `${schema_1.products.stock} <= ${schema_1.products.minStock}`));
        const [totalRevenueResult] = await db_1.db
            .select({ total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.total})::numeric` })
            .from(schema_1.sales);
        return {
            todaySales: todaySalesResult?.count || 0,
            todayRevenue: parseFloat(String(todaySalesResult?.total || '0')),
            monthSales: monthSalesResult?.count || 0,
            monthRevenue: parseFloat(String(monthSalesResult?.total || '0')),
            totalProducts: totalProductsResult?.count || 0,
            totalCustomers: totalCustomersResult?.count || 0,
            lowStockProducts: lowStockProductsResult?.count || 0,
            totalRevenue: parseFloat(String(totalRevenueResult?.total || '0'))
        };
    }
    async getTopSellingProducts(limit = 5) {
        return await db_1.db
            .select({
            productId: schema_1.saleItems.productId,
            name: schema_1.products.name,
            totalQuantity: (0, drizzle_orm_1.sql) `sum(${schema_1.saleItems.quantity})::int`,
            totalRevenue: (0, drizzle_orm_1.sql) `sum(${schema_1.saleItems.total})::numeric`
        })
            .from(schema_1.saleItems)
            .leftJoin(schema_1.products, (0, drizzle_orm_1.eq)(schema_1.saleItems.productId, schema_1.products.id))
            .groupBy(schema_1.saleItems.productId, schema_1.products.name)
            .orderBy((0, drizzle_orm_1.sql) `sum(${schema_1.saleItems.quantity}) DESC`)
            .limit(limit);
    }
    async getSalesChartData(days = 7) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        return await db_1.db
            .select({
            date: (0, drizzle_orm_1.sql) `date(${schema_1.sales.date})`,
            total: (0, drizzle_orm_1.sql) `sum(${schema_1.sales.total})::numeric`,
            count: (0, drizzle_orm_1.sql) `count(*)::int`
        })
            .from(schema_1.sales)
            .where((0, drizzle_orm_1.gte)(schema_1.sales.date, startDate))
            .groupBy((0, drizzle_orm_1.sql) `date(${schema_1.sales.date})`)
            .orderBy((0, drizzle_orm_1.sql) `date(${schema_1.sales.date})`);
    }
}
exports.DatabaseStorage = DatabaseStorage;
exports.storage = new DatabaseStorage();
