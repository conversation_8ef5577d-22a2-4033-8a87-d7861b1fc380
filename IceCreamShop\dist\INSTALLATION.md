# Golden Ice Cream Shop - Standalone Executable

## 📦 What's Included

This distribution contains a complete, standalone version of the Golden Ice Cream Shop management system. No additional software installation required!

### Files Overview:
- `icecream-shop.exe` - Main executable file (38MB)
- `start-app.bat` - Quick launch script for Windows
- `start-app.sh` - Launch script for Linux/Mac
- All necessary static files and components

## 🚀 Quick Start

### Windows Users:
1. Double-click `start-app.bat`
2. The application will launch automatically
3. Your browser will open to http://localhost:3000

### Linux/Mac Users:
1. Make executable: `chmod +x start-app.sh`
2. Run: `./start-app.sh`
3. <PERSON><PERSON><PERSON> will open automatically

## 🔑 Default Login

- **Username:** admin
- **Password:** admin123

⚠️ **Important:** Change the password immediately after first login!

## 🌟 Features

✅ **Complete POS System** - Sales, inventory, customers, reporting
✅ **Arabic Interface** - Full RTL support
✅ **Omani Rial Currency** - Pre-configured for OMR
✅ **PostgreSQL Database** - Professional data storage
✅ **No Installation Required** - Run directly from folder
✅ **Portable** - Copy folder to any computer
✅ **Cross-Platform** - Windows, Linux, Mac support

## 🔧 System Requirements

- **OS:** Windows 10/11 (64-bit), Linux, or macOS
- **RAM:** 4GB minimum (8GB recommended)
- **Storage:** 100MB free space
- **Network:** Local network access (optional)

## 📊 Technical Details

- **File Size:** 38MB compressed executable
- **Technology:** Node.js packaged with PKG
- **Database:** Embedded PostgreSQL with Drizzle ORM
- **Frontend:** Vanilla JavaScript with Bootstrap 5
- **Language:** Arabic (RTL) with English support

## 🛠️ Troubleshooting

### Application Won't Start
- Run as Administrator
- Check if port 3000 is available
- Disable antivirus temporarily

### Security Warning
- Click "More Info" → "Run Anyway"
- Add to antivirus exclusions

### Browser Shows Empty Page
- Wait 30 seconds for startup
- Refresh page (F5)
- Clear browser cache

## 🔄 Data Backup

Your data is automatically saved to the embedded database. For backups:
1. Copy the entire application folder
2. Or export data from Settings → Backup

## 📞 Support

For technical support, please provide:
- Operating system version
- Screenshot of any error messages
- Steps that led to the issue

---

**Ready to use immediately - no technical setup required!**