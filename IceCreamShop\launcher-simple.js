const express = require('express');
const path = require('path');

console.log('🍦 نظام إدارة محل الآيس كريم');
console.log('═══════════════════════════════════════════════════════════');

const app = express();
const PORT = process.env.PORT || 3000;

// Static files
app.use(express.static('.'));
app.use(express.json());

// API Routes
const serverRoutes = require('./server-routes');
app.use('/api', serverRoutes);

// Main route
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`🌐 رابط التطبيق: http://localhost:${PORT}`);
    console.log('📱 للوصول من الأجهزة الأخرى: http://YOUR_IP:${PORT}');
    console.log('═══════════════════════════════════════════════════════════');
    console.log('💡 نصيحة: افتح المتصفح وانتقل إلى http://localhost:' + PORT);
    console.log('🔐 اسم المستخدم: admin - كلمة المرور: admin123');
    console.log('═══════════════════════════════════════════════════════════');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});