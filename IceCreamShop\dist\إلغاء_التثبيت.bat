@echo off
chcp 65001 > nul
title إلغاء تثبيت نظام إدارة محل الآيس كريم
color 0C

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                      🗑️  إلغاء تثبيت نظام إدارة محل الآيس كريم              ║
echo ║                          Golden Ice Cream Shop Uninstaller                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo ⚠️  تحذير: سيتم حذف جميع ملفات التطبيق!
echo.
echo 💾 هل تريد عمل نسخة احتياطية من البيانات؟
set /p "backup=اكتب 'y' للنسخ الاحتياطي أو 'n' للمتابعة بدون نسخ: "

if /i "%backup%"=="y" (
    echo 📋 إنشاء نسخة احتياطية...
    set "backup_path=%USERPROFILE%\Desktop\IceCreamShop_Backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%"
    mkdir "%backup_path%" 2>nul
    
    REM Copy important files for backup
    if exist "data" xcopy "data" "%backup_path%\data" /E /I /Q >nul
    if exist "*.json" copy "*.json" "%backup_path%\" >nul
    if exist "*.db" copy "*.db" "%backup_path%\" >nul
    
    echo ✅ تم إنشاء نسخة احتياطية في: %backup_path%
    echo.
)

echo 🛑 إيقاف التطبيق إذا كان يعمل...
taskkill /f /im "icecream-shop.exe" >nul 2>&1

echo.
echo 🗑️  بدء إلغاء التثبيت...
echo.

REM Remove desktop shortcut
set "shortcut_path=%USERPROFILE%\Desktop\نظام الآيس كريم.lnk"
if exist "%shortcut_path%" (
    del "%shortcut_path%" >nul 2>&1
    echo ✅ تم حذف اختصار سطح المكتب
)

REM Remove start menu entry
set "startmenu_path=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Ice Cream Shop"
if exist "%startmenu_path%" (
    rmdir /s /q "%startmenu_path%" >nul 2>&1
    echo ✅ تم حذف مدخل قائمة ابدأ
)

REM Get current directory (installation path)
set "install_path=%~dp0"

echo.
echo 📁 مجلد التثبيت: %install_path%
echo.
echo ⚠️  هل أنت متأكد من إلغاء التثبيت؟
echo    سيتم حذف جميع الملفات في هذا المجلد!
echo.
set /p "confirm=اكتب 'DELETE' بالأحرف الكبيرة للتأكيد: "

if not "%confirm%"=="DELETE" (
    echo.
    echo ❌ تم إلغاء العملية
    echo.
    pause
    exit /b 0
)

echo.
echo 🗑️  حذف الملفات...

REM Delete application files
del "icecream-shop.exe" >nul 2>&1
del "ابدأ_هنا.bat" >nul 2>&1
del "تشغيل_التطبيق.bat" >nul 2>&1
del "تشغيل_كمدير.bat" >nul 2>&1
del "اقرأني_أولاً.txt" >nul 2>&1
del "حل_المشاكل.md" >nul 2>&1
del "*.html" >nul 2>&1
del "*.css" >nul 2>&1
del "*.js" >nul 2>&1
del "*.json" >nul 2>&1
del "*.md" >nul 2>&1

REM Delete directories
if exist "assets" rmdir /s /q "assets" >nul 2>&1
if exist "components" rmdir /s /q "components" >nul 2>&1
if exist "utils" rmdir /s /q "utils" >nul 2>&1
if exist "data" rmdir /s /q "data" >nul 2>&1

echo ✅ تم حذف ملفات التطبيق

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          ✅ تم إلغاء التثبيت بنجاح!                         ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

if /i "%backup%"=="y" (
    echo 💾 النسخة الاحتياطية محفوظة في: %backup_path%
    echo.
)

echo 🙏 شكراً لاستخدام نظام إدارة محل الآيس كريم!
echo.
echo ⚠️  سيتم حذف هذا الملف خلال 10 ثوانٍ...

timeout /t 10 /nobreak > nul

REM Delete this uninstaller file itself
(goto) 2>nul & del "%~f0"
