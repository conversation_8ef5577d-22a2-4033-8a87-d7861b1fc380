# 🔧 دليل حل المشاكل - نظام إدارة محل الآيس كريم

## المشكلة: "How do you want to open this file?"

### السبب:
هذه الرسالة تظهر عندما يحاول Windows فتح الملف التنفيذي `.exe` ولكن لا يتعرف عليه كبرنامج صالح للتشغيل.

---

## 🛠️ الحلول المتاحة

### الحل الأول: استخدام ملف التشغيل المحسن ⭐ (الأفضل)
```
انقر نقراً مزدوجاً على: تشغيل_التطبيق.bat
```

### الحل الثاني: التشغيل بصلاحيات المدير
```
انقر بالزر الأيمن على: تشغيل_كمدير.bat
اختر: "تشغيل كمسؤول" أو "Run as administrator"
```

### الحل الثالث: استخدام PowerShell
```
انقر بالزر الأيمن على: تشغيل_التطبيق.ps1
اختر: "تشغيل باستخدام PowerShell"
```

### الحل الرابع: التشغيل من موجه الأوامر
1. اضغط `Win + R`
2. اكتب `cmd` واضغط Enter
3. انتقل لمجلد التطبيق:
   ```cmd
   cd "C:\path\to\your\icecream\folder"
   ```
4. شغل الأمر:
   ```cmd
   icecream-shop.exe
   ```

---

## 🔒 مشاكل الأمان والحماية

### المشكلة: Windows Defender يحجب الملف
**الحل:**
1. افتح Windows Security
2. اذهب إلى "Virus & threat protection"
3. انقر "Manage settings" تحت "Virus & threat protection settings"
4. أضف مجلد التطبيق إلى "Exclusions"

### المشكلة: SmartScreen يحجب التطبيق
**الحل:**
1. عند ظهور رسالة SmartScreen
2. انقر "More info"
3. انقر "Run anyway"

### المشكلة: برنامج مكافحة الفيروسات يحجب الملف
**الحل:**
1. أضف الملف `icecream-shop.exe` إلى قائمة الاستثناءات
2. أو أضف المجلد بالكامل إلى قائمة المجلدات الآمنة

---

## 🌐 مشاكل الشبكة والاتصال

### المشكلة: "Port 3000 is already in use"
**الحل:**
1. أغلق أي تطبيق آخر يستخدم المنفذ 3000
2. أو أعد تشغيل الكمبيوتر
3. أو استخدم الأمر:
   ```cmd
   netstat -ano | findstr :3000
   taskkill /PID [رقم_العملية] /F
   ```

### المشكلة: لا يفتح المتصفح تلقائياً
**الحل:**
1. افتح المتصفح يدوياً
2. اذهب إلى: `http://localhost:3000`
3. أو جرب: `http://127.0.0.1:3000`

---

## 💻 مشاكل النظام

### المشكلة: "This app can't run on your PC"
**الحل:**
- تأكد من أن نظامك Windows 10/11 (64-bit)
- قم بتحديث Windows إلى آخر إصدار

### المشكلة: الملف لا يعمل على الإطلاق
**الحل:**
1. تحقق من أن الملف غير تالف:
   - حجم الملف يجب أن يكون حوالي 30-50 ميجابايت
   - تاريخ الإنشاء حديث
2. أعد تنزيل الملف إذا لزم الأمر
3. جرب تشغيله من مجلد مختلف

---

## 🔄 خطوات استكشاف الأخطاء المتقدمة

### الخطوة 1: فحص سلامة الملف
```cmd
dir icecream-shop.exe
```
يجب أن يظهر حجم الملف (حوالي 30-50 MB)

### الخطوة 2: فحص العمليات النشطة
```cmd
tasklist | findstr icecream
```

### الخطوة 3: فحص المنافذ المستخدمة
```cmd
netstat -an | findstr :3000
```

### الخطوة 4: تشغيل تجريبي
```cmd
icecream-shop.exe --help
```

---

## 📞 طلب المساعدة

إذا لم تنجح أي من الحلول أعلاه، يرجى تقديم المعلومات التالية:

1. **نظام التشغيل**: Windows 10/11 (32/64 bit)
2. **رسالة الخطأ الكاملة**: (نسخ ولصق)
3. **حجم الملف**: `icecream-shop.exe`
4. **برامج مكافحة الفيروسات المثبتة**
5. **هل جربت التشغيل كمدير؟**

---

## ✅ قائمة مراجعة سريعة

- [ ] جربت ملف `تشغيل_التطبيق.bat`
- [ ] جربت التشغيل كمدير
- [ ] أضفت التطبيق لاستثناءات مكافح الفيروسات
- [ ] تأكدت من عدم استخدام المنفذ 3000
- [ ] جربت فتح المتصفح يدوياً على localhost:3000
- [ ] أعدت تشغيل الكمبيوتر

---

**💡 نصيحة**: الطريقة الأسهل والأكثر أماناً هي استخدام ملف `تشغيل_التطبيق.bat` بدلاً من النقر المباشر على الملف التنفيذي.
