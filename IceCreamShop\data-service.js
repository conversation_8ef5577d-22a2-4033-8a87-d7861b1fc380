// Unified Data Service - Fixes all data conflicts and type issues
class DataService {
    constructor() {
        this.products = [];
        this.sales = [];
        this.customers = [];
        this.users = [];
        this.inventory = [];
        this.settings = {};
        this.initializeDefaultData();
    }

    initializeDefaultData() {
        // Initialize default products
        this.products = [
            {
                id: 1,
                name: 'آيس كريم الفانيليا',
                nameEn: 'Vanilla Ice Cream',
                category: 'كوب',
                flavor: 'فانيليا',
                price: 1.500,
                cost: 0.800,
                stock: 50,
                minStock: 10,
                description: 'آيس كريم الفانيليا الكلاسيكي الطبيعي',
                image: '🍦',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: 2,
                name: 'آيس كريم الشوكولاتة',
                nameEn: 'Chocolate Ice Cream',
                category: 'كوب',
                flavor: 'شوكولاتة',
                price: 1.750,
                cost: 0.950,
                stock: 45,
                minStock: 10,
                description: 'آيس كريم الشوكولاتة الغنية',
                image: '🍫',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: 3,
                name: 'آيس كريم الفراولة',
                nameEn: 'Strawberry Ice Cream',
                category: 'كوب',
                flavor: 'فراولة',
                price: 1.600,
                cost: 0.850,
                stock: 30,
                minStock: 10,
                description: 'آيس كريم الفراولة الطازجة',
                image: '🍓',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: 4,
                name: 'آيس كريم المانجو',
                nameEn: 'Mango Ice Cream',
                category: 'كوب',
                flavor: 'مانجو',
                price: 1.800,
                cost: 1.000,
                stock: 25,
                minStock: 10,
                description: 'آيس كريم المانجو الاستوائي',
                image: '🥭',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: 5,
                name: 'آيس كريم النعناع',
                nameEn: 'Mint Ice Cream',
                category: 'كوب',
                flavor: 'نعناع',
                price: 1.650,
                cost: 0.900,
                stock: 35,
                minStock: 10,
                description: 'آيس كريم النعناع المنعش',
                image: '🌿',
                isActive: true,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        // Initialize sample customers
        this.customers = [
            {
                id: 1,
                name: 'أحمد محمد',
                phone: '96899123456',
                email: '<EMAIL>',
                address: 'مسقط، العذيبة',
                membershipType: 'ذهبي',
                totalPurchases: 25.500,
                visits: 12,
                lastVisit: new Date(),
                discount: 10,
                createdAt: new Date()
            },
            {
                id: 2,
                name: 'فاطمة علي',
                phone: '96899654321',
                email: '<EMAIL>',
                address: 'مسقط، القرم',
                membershipType: 'فضي',
                totalPurchases: 15.750,
                visits: 8,
                lastVisit: new Date(),
                discount: 5,
                createdAt: new Date()
            }
        ];

        // Initialize default users
        this.users = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                name: 'المدير',
                role: 'admin',
                permissions: ['all'],
                isActive: true,
                createdAt: new Date(),
                lastLogin: new Date()
            },
            {
                id: 2,
                username: 'cashier',
                password: 'cashier123',
                name: 'الكاشير',
                role: 'cashier',
                permissions: ['sales', 'customers'],
                isActive: true,
                createdAt: new Date(),
                lastLogin: null
            }
        ];

        // Initialize default settings
        this.settings = {
            shopName: 'محل الآيس كريم الذهبي',
            shopNameEn: 'Golden Ice Cream Shop',
            currency: 'OMR',
            currencySymbol: 'ر.ع',
            taxRate: 0.05,
            receiptHeader: 'محل الآيس كريم الذهبي',
            receiptFooter: 'شكراً لزيارتكم',
            lowStockAlert: true,
            autoBackup: true,
            language: 'ar'
        };

        // Initialize sample sales
        this.generateSampleSales();
    }

    generateSampleSales() {
        const today = new Date();
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        
        // Generate sales for current month
        for (let i = 0; i < 50; i++) {
            const saleDate = new Date(thisMonth.getTime() + Math.random() * (today.getTime() - thisMonth.getTime()));
            const items = this.generateRandomSaleItems();
            
            this.sales.push({
                id: i + 1,
                transactionId: `TXN${String(i + 1).padStart(6, '0')}`,
                date: saleDate,
                items: items,
                subtotal: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                tax: 0,
                discount: 0,
                total: items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                paymentMethod: ['نقداً', 'بطاقة', 'تحويل'][Math.floor(Math.random() * 3)],
                customerId: Math.random() > 0.5 ? Math.floor(Math.random() * 2) + 1 : null,
                userId: 1,
                status: 'completed',
                createdAt: saleDate
            });
        }
    }

    generateRandomSaleItems() {
        const numItems = Math.floor(Math.random() * 3) + 1;
        const items = [];
        
        for (let i = 0; i < numItems; i++) {
            const product = this.products[Math.floor(Math.random() * this.products.length)];
            const quantity = Math.floor(Math.random() * 3) + 1;
            
            items.push({
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity: quantity,
                subtotal: product.price * quantity
            });
        }
        
        return items;
    }

    // Product Management
    getAllProducts() {
        return this.products.filter(p => p.isActive);
    }

    getProductById(id) {
        return this.products.find(p => p.id === id);
    }

    addProduct(productData) {
        const newProduct = {
            id: Math.max(...this.products.map(p => p.id), 0) + 1,
            ...productData,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        this.products.push(newProduct);
        this.saveToStorage();
        return newProduct;
    }

    updateProduct(id, productData) {
        const index = this.products.findIndex(p => p.id === id);
        if (index !== -1) {
            this.products[index] = {
                ...this.products[index],
                ...productData,
                updatedAt: new Date()
            };
            this.saveToStorage();
            return this.products[index];
        }
        return null;
    }

    deleteProduct(id) {
        const index = this.products.findIndex(p => p.id === id);
        if (index !== -1) {
            this.products[index].isActive = false;
            this.saveToStorage();
            return true;
        }
        return false;
    }

    // Sales Management
    getAllSales() {
        return this.sales.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    getSalesByDateRange(startDate, endDate) {
        return this.sales.filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate >= startDate && saleDate <= endDate;
        });
    }

    createSale(saleData) {
        const newSale = {
            id: Math.max(...this.sales.map(s => s.id), 0) + 1,
            transactionId: `TXN${String(this.sales.length + 1).padStart(6, '0')}`,
            date: new Date(),
            ...saleData,
            status: 'completed',
            createdAt: new Date()
        };
        
        // Update inventory
        saleData.items.forEach(item => {
            const product = this.products.find(p => p.id === item.productId);
            if (product) {
                product.stock -= item.quantity;
            }
        });
        
        this.sales.push(newSale);
        this.saveToStorage();
        return newSale;
    }

    // Customer Management
    getAllCustomers() {
        return this.customers;
    }

    getCustomerById(id) {
        return this.customers.find(c => c.id === id);
    }

    addCustomer(customerData) {
        const newCustomer = {
            id: Math.max(...this.customers.map(c => c.id), 0) + 1,
            ...customerData,
            totalPurchases: 0,
            visits: 0,
            createdAt: new Date()
        };
        
        this.customers.push(newCustomer);
        this.saveToStorage();
        return newCustomer;
    }

    updateCustomer(id, customerData) {
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
            this.customers[index] = {
                ...this.customers[index],
                ...customerData,
                updatedAt: new Date()
            };
            this.saveToStorage();
            return this.customers[index];
        }
        return null;
    }

    // User Management
    getAllUsers() {
        return this.users;
    }

    getUserById(id) {
        return this.users.find(u => u.id === id);
    }

    addUser(userData) {
        const newUser = {
            id: Math.max(...this.users.map(u => u.id), 0) + 1,
            ...userData,
            isActive: true,
            createdAt: new Date(),
            lastLogin: null
        };
        
        this.users.push(newUser);
        this.saveToStorage();
        return newUser;
    }

    updateUser(id, userData) {
        const index = this.users.findIndex(u => u.id === id);
        if (index !== -1) {
            this.users[index] = {
                ...this.users[index],
                ...userData,
                updatedAt: new Date()
            };
            this.saveToStorage();
            return this.users[index];
        }
        return null;
    }

    // Analytics and Reports
    getDashboardStats() {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        
        const todaySales = this.sales.filter(sale => new Date(sale.date) >= startOfDay);
        const monthSales = this.sales.filter(sale => new Date(sale.date) >= startOfMonth);
        
        return {
            todaySales: todaySales.length,
            todayRevenue: todaySales.reduce((sum, sale) => sum + sale.total, 0),
            monthSales: monthSales.length,
            monthRevenue: monthSales.reduce((sum, sale) => sum + sale.total, 0),
            totalProducts: this.products.filter(p => p.isActive).length,
            totalCustomers: this.customers.length,
            lowStockProducts: this.products.filter(p => p.stock <= p.minStock).length,
            totalRevenue: this.sales.reduce((sum, sale) => sum + sale.total, 0)
        };
    }

    getTopSellingProducts(limit = 5) {
        const productSales = {};
        
        this.sales.forEach(sale => {
            sale.items.forEach(item => {
                if (!productSales[item.productId]) {
                    productSales[item.productId] = {
                        productId: item.productId,
                        name: item.name,
                        totalQuantity: 0,
                        totalRevenue: 0
                    };
                }
                productSales[item.productId].totalQuantity += item.quantity;
                productSales[item.productId].totalRevenue += item.subtotal;
            });
        });
        
        return Object.values(productSales)
            .sort((a, b) => b.totalQuantity - a.totalQuantity)
            .slice(0, limit);
    }

    getSalesChartData(days = 7) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - days + 1);
        
        const salesByDate = {};
        
        // Initialize dates
        for (let i = 0; i < days; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            const dateKey = date.toISOString().split('T')[0];
            salesByDate[dateKey] = 0;
        }
        
        // Calculate sales for each date
        this.sales.forEach(sale => {
            const dateKey = new Date(sale.date).toISOString().split('T')[0];
            if (salesByDate.hasOwnProperty(dateKey)) {
                salesByDate[dateKey] += sale.total;
            }
        });
        
        return {
            labels: Object.keys(salesByDate).map(date => 
                new Date(date).toLocaleDateString('ar-OM', { month: 'short', day: 'numeric' })
            ),
            data: Object.values(salesByDate)
        };
    }

    // Settings Management
    getSettings() {
        return this.settings;
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveToStorage();
        return this.settings;
    }

    // Data Persistence
    saveToStorage() {
        try {
            localStorage.setItem('icecreamshop_data', JSON.stringify({
                products: this.products,
                sales: this.sales,
                customers: this.customers,
                users: this.users,
                settings: this.settings
            }));
        } catch (error) {
            console.error('Error saving data to localStorage:', error);
        }
    }

    loadFromStorage() {
        try {
            const data = localStorage.getItem('icecreamshop_data');
            if (data) {
                const parsed = JSON.parse(data);
                this.products = parsed.products || this.products;
                this.sales = parsed.sales || this.sales;
                this.customers = parsed.customers || this.customers;
                this.users = parsed.users || this.users;
                this.settings = parsed.settings || this.settings;
            }
        } catch (error) {
            console.error('Error loading data from localStorage:', error);
        }
    }

    // Export/Import functionality
    exportData() {
        const data = {
            products: this.products,
            sales: this.sales,
            customers: this.customers,
            users: this.users,
            settings: this.settings,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `icecreamshop_backup_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.products) this.products = data.products;
            if (data.sales) this.sales = data.sales;
            if (data.customers) this.customers = data.customers;
            if (data.users) this.users = data.users;
            if (data.settings) this.settings = data.settings;
            
            this.saveToStorage();
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }
}
