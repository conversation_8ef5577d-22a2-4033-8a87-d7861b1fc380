const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 بدء إنشاء الملف التنفيذي...');

// Create dist directory
if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist');
}

// Copy static files to dist
const filesToCopy = [
    'index.html',
    'styles.css',
    'app.js',
    'data-service.js',
    'server-routes.js',
    'assets',
    'components',
    'utils',
    'README.md',
    'دليل_التثبيت.md',
    'خطوات_سريعة.md'
];

console.log('📁 نسخ الملفات الضرورية...');

filesToCopy.forEach(file => {
    const srcPath = path.join(__dirname, file);
    const destPath = path.join(__dirname, 'dist', file);
    
    if (fs.existsSync(srcPath)) {
        if (fs.lstatSync(srcPath).isDirectory()) {
            // Copy directory recursively
            execSync(`cp -r "${srcPath}" "${destPath}"`, { stdio: 'inherit' });
        } else {
            // Copy file
            fs.copyFileSync(srcPath, destPath);
        }
        console.log(`✅ تم نسخ: ${file}`);
    }
});

// Create launcher scripts for dist
const launcherScripts = {
    'start-app.bat': `@echo off
chcp 65001 > nul
title نظام إدارة محل الآيس كريم

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       🍦 نظام إدارة محل الآيس كريم                          ║
echo ║                          Golden Ice Cream Shop                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 بدء تشغيل التطبيق...

.\\icecream-shop.exe

echo.
echo 🛑 تم إيقاف التطبيق
pause`,

    'start-app.sh': `#!/bin/bash

clear
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                       🍦 نظام إدارة محل الآيس كريم                          ║"
echo "║                          Golden Ice Cream Shop                               ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo
echo "🚀 بدء تشغيل التطبيق..."

./icecream-shop

echo
echo "🛑 تم إيقاف التطبيق"
read -p "اضغط Enter للمتابعة..."`,

    'README-DIST.md': `# نظام إدارة محل الآيس كريم الذهبي - الإصدار القابل للتنفيذ

## 🚀 التشغيل السريع

### على Windows:
انقر نقراً مزدوجاً على \`start-app.bat\`

### على Linux/Mac:
شغل الأمر: \`./start-app.sh\`

## 🔐 بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin123

## 🌐 الوصول للتطبيق
http://localhost:3000

## 📋 متطلبات النظام
- Windows 10/11, macOS 10.14+, أو Linux Ubuntu 18.04+
- 4 جيجابايت رام أو أكثر
- 500 ميجابايت مساحة قرص صلب

## 🔧 المشاكل الشائعة
- إذا لم يفتح التطبيق، تأكد من إغلاق أي برامج تستخدم المنفذ 3000
- إذا ظهرت مشاكل أمان، اسمح للتطبيق بالوصول للشبكة
`
};

Object.entries(launcherScripts).forEach(([filename, content]) => {
    const filePath = path.join(__dirname, 'dist', filename);
    fs.writeFileSync(filePath, content);
    console.log(`✅ تم إنشاء: ${filename}`);
    
    // Make shell scripts executable
    if (filename.endsWith('.sh')) {
        execSync(`chmod +x "${filePath}"`);
    }
});

console.log('✅ تم إعداد ملفات التوزيع بنجاح!');