@echo off
chcp 65001 > nul
title Ice Cream Shop - ابدأ هنا
color 0A
cls

echo.
echo    🍦 نظام إدارة محل الآيس كريم 🍦
echo    ================================
echo.
echo    🔍 التحقق من الملفات...

REM Change to the directory where the batch file is located
cd /d "%~dp0"

REM Check if exe exists
if not exist "icecream-shop.exe" (
    echo    ❌ خطأ: الملف التنفيذي غير موجود!
    echo.
    pause
    exit /b 1
)

echo    ✅ الملف موجود (حجم: 39 MB)
echo.
echo    🚀 تشغيل التطبيق...
echo.

REM Kill any existing instances first
taskkill /f /im "icecream-shop.exe" >nul 2>&1

REM Start the exe file
start "" "icecream-shop.exe"

REM Wait a moment
timeout /t 3 /nobreak > nul

echo    ✅ تم تشغيل التطبيق!
echo.
echo    🌐 افتح المتصفح واذهب إلى:
echo       http://localhost:3000
echo.
echo    🔐 بيانات الدخول:
echo       المستخدم: admin
echo       كلمة المرور: admin123
echo.
echo    ⚠️  لا تغلق هذه النافذة!
echo.

timeout /t 10 /nobreak > nul
