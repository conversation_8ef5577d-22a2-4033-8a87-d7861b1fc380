"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.inventoryTransactionsRelations = exports.usersRelations = exports.customersRelations = exports.saleItemsRelations = exports.salesRelations = exports.productsRelations = exports.inventoryTransactions = exports.settings = exports.users = exports.customers = exports.saleItems = exports.sales = exports.products = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_orm_1 = require("drizzle-orm");
// Products table
exports.products = (0, pg_core_1.pgTable)('products', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    name: (0, pg_core_1.text)('name').notNull(),
    category: (0, pg_core_1.text)('category').notNull(),
    price: (0, pg_core_1.decimal)('price', { precision: 10, scale: 3 }).notNull(),
    cost: (0, pg_core_1.decimal)('cost', { precision: 10, scale: 3 }).notNull(),
    stock: (0, pg_core_1.integer)('stock').notNull().default(0),
    minStock: (0, pg_core_1.integer)('min_stock').notNull().default(0),
    description: (0, pg_core_1.text)('description'),
    isActive: (0, pg_core_1.boolean)('is_active').notNull().default(true),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow(),
});
// Sales table
exports.sales = (0, pg_core_1.pgTable)('sales', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    transactionId: (0, pg_core_1.varchar)('transaction_id', { length: 50 }).notNull().unique(),
    customerId: (0, pg_core_1.integer)('customer_id').references(() => exports.customers.id),
    userId: (0, pg_core_1.integer)('user_id').references(() => exports.users.id),
    subtotal: (0, pg_core_1.decimal)('subtotal', { precision: 10, scale: 3 }).notNull(),
    discount: (0, pg_core_1.decimal)('discount', { precision: 10, scale: 3 }).notNull().default('0'),
    tax: (0, pg_core_1.decimal)('tax', { precision: 10, scale: 3 }).notNull().default('0'),
    total: (0, pg_core_1.decimal)('total', { precision: 10, scale: 3 }).notNull(),
    paymentMethod: (0, pg_core_1.varchar)('payment_method', { length: 50 }).notNull(),
    status: (0, pg_core_1.varchar)('status', { length: 20 }).notNull().default('completed'),
    date: (0, pg_core_1.timestamp)('date').defaultNow(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
});
// Sale items table
exports.saleItems = (0, pg_core_1.pgTable)('sale_items', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    saleId: (0, pg_core_1.integer)('sale_id').references(() => exports.sales.id).notNull(),
    productId: (0, pg_core_1.integer)('product_id').references(() => exports.products.id).notNull(),
    quantity: (0, pg_core_1.integer)('quantity').notNull(),
    price: (0, pg_core_1.decimal)('price', { precision: 10, scale: 3 }).notNull(),
    total: (0, pg_core_1.decimal)('total', { precision: 10, scale: 3 }).notNull(),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
});
// Customers table
exports.customers = (0, pg_core_1.pgTable)('customers', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    name: (0, pg_core_1.text)('name').notNull(),
    phone: (0, pg_core_1.varchar)('phone', { length: 20 }),
    email: (0, pg_core_1.varchar)('email', { length: 100 }),
    address: (0, pg_core_1.text)('address'),
    membershipType: (0, pg_core_1.varchar)('membership_type', { length: 20 }).notNull().default('عادي'),
    discount: (0, pg_core_1.integer)('discount').notNull().default(0),
    totalPurchases: (0, pg_core_1.decimal)('total_purchases', { precision: 10, scale: 3 }).notNull().default('0'),
    visits: (0, pg_core_1.integer)('visits').notNull().default(0),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow(),
});
// Users table
exports.users = (0, pg_core_1.pgTable)('users', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    username: (0, pg_core_1.varchar)('username', { length: 50 }).notNull().unique(),
    password: (0, pg_core_1.varchar)('password', { length: 255 }).notNull(),
    name: (0, pg_core_1.text)('name').notNull(),
    role: (0, pg_core_1.varchar)('role', { length: 20 }).notNull().default('cashier'),
    permissions: (0, pg_core_1.json)('permissions').$type(),
    isActive: (0, pg_core_1.boolean)('is_active').notNull().default(true),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow(),
    lastLogin: (0, pg_core_1.timestamp)('last_login'),
});
// Settings table
exports.settings = (0, pg_core_1.pgTable)('settings', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    key: (0, pg_core_1.varchar)('key', { length: 100 }).notNull().unique(),
    value: (0, pg_core_1.text)('value').notNull(),
    type: (0, pg_core_1.varchar)('type', { length: 20 }).notNull().default('string'),
    description: (0, pg_core_1.text)('description'),
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)('updated_at').defaultNow(),
});
// Inventory transactions table
exports.inventoryTransactions = (0, pg_core_1.pgTable)('inventory_transactions', {
    id: (0, pg_core_1.serial)('id').primaryKey(),
    productId: (0, pg_core_1.integer)('product_id').references(() => exports.products.id).notNull(),
    userId: (0, pg_core_1.integer)('user_id').references(() => exports.users.id),
    type: (0, pg_core_1.varchar)('type', { length: 20 }).notNull(), // 'in', 'out', 'adjustment'
    quantity: (0, pg_core_1.integer)('quantity').notNull(),
    reason: (0, pg_core_1.text)('reason'),
    reference: (0, pg_core_1.varchar)('reference', { length: 100 }), // Sale ID or other reference
    createdAt: (0, pg_core_1.timestamp)('created_at').defaultNow(),
});
// Relations
exports.productsRelations = (0, drizzle_orm_1.relations)(exports.products, ({ many }) => ({
    saleItems: many(exports.saleItems),
    inventoryTransactions: many(exports.inventoryTransactions),
}));
exports.salesRelations = (0, drizzle_orm_1.relations)(exports.sales, ({ one, many }) => ({
    customer: one(exports.customers, {
        fields: [exports.sales.customerId],
        references: [exports.customers.id],
    }),
    user: one(exports.users, {
        fields: [exports.sales.userId],
        references: [exports.users.id],
    }),
    items: many(exports.saleItems),
}));
exports.saleItemsRelations = (0, drizzle_orm_1.relations)(exports.saleItems, ({ one }) => ({
    sale: one(exports.sales, {
        fields: [exports.saleItems.saleId],
        references: [exports.sales.id],
    }),
    product: one(exports.products, {
        fields: [exports.saleItems.productId],
        references: [exports.products.id],
    }),
}));
exports.customersRelations = (0, drizzle_orm_1.relations)(exports.customers, ({ many }) => ({
    sales: many(exports.sales),
}));
exports.usersRelations = (0, drizzle_orm_1.relations)(exports.users, ({ many }) => ({
    sales: many(exports.sales),
    inventoryTransactions: many(exports.inventoryTransactions),
}));
exports.inventoryTransactionsRelations = (0, drizzle_orm_1.relations)(exports.inventoryTransactions, ({ one }) => ({
    product: one(exports.products, {
        fields: [exports.inventoryTransactions.productId],
        references: [exports.products.id],
    }),
    user: one(exports.users, {
        fields: [exports.inventoryTransactions.userId],
        references: [exports.users.id],
    }),
}));
