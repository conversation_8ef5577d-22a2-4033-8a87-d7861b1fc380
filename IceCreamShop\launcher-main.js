const express = require('express');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

console.log('🍦 نظام إدارة محل الآيس كريم');
console.log('═══════════════════════════════════════════════════════════');

const app = express();
const PORT = process.env.PORT || 3000;

// Static files
app.use(express.static('.'));
app.use(express.json());

// API Routes
const serverRoutes = require('./server-routes');
app.use('/api', serverRoutes);

// Main route
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`🌐 رابط التطبيق: http://localhost:${PORT}`);
    console.log('═══════════════════════════════════════════════════════════');
    
    // Auto-open browser
    setTimeout(() => {
        const url = `http://localhost:${PORT}`;
        try {
            if (process.platform === 'win32') {
                spawn('cmd', ['/c', 'start', url], { detached: true, stdio: 'ignore' });
            } else if (process.platform === 'darwin') {
                spawn('open', [url], { detached: true, stdio: 'ignore' });
            } else {
                spawn('xdg-open', [url], { detached: true, stdio: 'ignore' });
            }
            console.log('🌐 فتح المتصفح تلقائياً...');
        } catch (error) {
            console.log('⚠️  لم يتم فتح المتصفح تلقائياً، افتح المتصفح وانتقل إلى:', url);
        }
    }, 2000);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف الخادم...');
    process.exit(0);
});