#!/usr/bin/env node

const express = require('express');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Configuration
const PORT = 3000;
const APP_NAME = 'نظام إدارة محل الآيس كريم';

console.log('🍦 ' + APP_NAME);
console.log('═══════════════════════════════════════════════════════════');

// Check if running as standalone
const isStandalone = !process.env.REPLIT_ENVIRONMENT;

// Create Express app
const app = express();

// Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.static('.'));

// Import server routes
const serverRoutes = require('./server-routes');
app.use('/api', serverRoutes);

// Serve main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`🌐 رابط التطبيق: http://localhost:${PORT}`);
    console.log(`📱 للوصول من الأجهزة الأخرى: http://YOUR_IP:${PORT}`);
    console.log('═══════════════════════════════════════════════════════════');
    
    if (isStandalone) {
        console.log('🖥️  التطبيق يعمل في وضع سطح المكتب');
        console.log('📝 بيانات الدخول الافتراضية:');
        console.log('   اسم المستخدم: admin');
        console.log('   كلمة المرور: admin123');
        console.log('');
        console.log('⚡ للإيقاف اضغط Ctrl+C');
        console.log('═══════════════════════════════════════════════════════════');
        
        // Try to open browser automatically
        openBrowser(`http://localhost:${PORT}`);
    }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

// Function to open browser
function openBrowser(url) {
    const platform = process.platform;
    let command;
    
    switch (platform) {
        case 'win32':
            command = 'start';
            break;
        case 'darwin':
            command = 'open';
            break;
        default:
            command = 'xdg-open';
    }
    
    try {
        spawn(command, [url], { 
            detached: true, 
            stdio: 'ignore' 
        }).unref();
        console.log(`🚀 فتح المتصفح تلقائياً: ${url}`);
    } catch (error) {
        console.log(`⚠️  لم يتم فتح المتصفح تلقائياً. يرجى فتح الرابط يدوياً: ${url}`);
    }
}

// Display startup banner
console.log(`
🍦 ${APP_NAME}
═══════════════════════════════════════════════════════════
نظام شامل لإدارة محلات الآيس كريم
• إدارة المبيعات والمخزون
• متابعة العملاء والتقارير  
• واجهة عربية سهلة الاستخدام
• يدعم الريال العماني
═══════════════════════════════════════════════════════════
`);

module.exports = app;