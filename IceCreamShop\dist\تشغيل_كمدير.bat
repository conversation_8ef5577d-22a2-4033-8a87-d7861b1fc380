@echo off
chcp 65001 > nul

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل البرنامج بصلاحيات المدير
    goto :run_app
) else (
    echo 🔐 يتطلب صلاحيات المدير...
    echo 🔄 إعادة تشغيل بصلاحيات المدير...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:run_app
title نظام إدارة محل الآيس كريم - وضع المدير
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       🍦 نظام إدارة محل الآيس كريم                          ║
echo ║                          Golden Ice Cream Shop                               ║
echo ║                         🔐 وضع المدير المتقدم                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Change to script directory
cd /d "%~dp0"

echo 🔍 التحقق من الملفات...
if not exist "icecream-shop.exe" (
    echo ❌ خطأ: الملف التنفيذي غير موجود
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo.
echo 🚀 تشغيل التطبيق بصلاحيات المدير...
echo.

REM Kill any existing instances
taskkill /f /im "icecream-shop.exe" >nul 2>&1

REM Start the application
"icecream-shop.exe"

echo.
echo 🛑 تم إيقاف التطبيق
pause
