const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const router = express.Router();

// Check if we should use database or file storage
const USE_DATABASE = process.env.DATABASE_URL ? true : false;

// Data storage file (fallback)
const DATA_FILE = path.join(__dirname, 'icecreamshop_data.json');

// Database storage (if available)
let storage = null;
if (USE_DATABASE) {
  try {
    const { storage: dbStorage } = require('./dist/server/storage');
    storage = dbStorage;
    console.log('🗄️  Using PostgreSQL database storage');
  } catch (error) {
    console.error('❌ Database connection failed, falling back to file storage:', error.message);
  }
}

if (!storage) {
  console.log('📁 Using file-based storage');
}

// Helper function to read data
async function readData() {
    try {
        const data = await fs.readFile(DATA_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        // Return default data if file doesn't exist
        return {
            products: [
                {
                    id: 1,
                    name: 'آيس كريم الفانيليا',
                    category: 'آيس كريم',
                    price: 2.5,
                    cost: 1.2,
                    stock: 50,
                    minStock: 10,
                    description: 'آيس كريم فانيليا طبيعي',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'آيس كريم الشوكولاتة',
                    category: 'آيس كريم',
                    price: 3.0,
                    cost: 1.5,
                    stock: 40,
                    minStock: 10,
                    description: 'آيس كريم شوكولاتة بلجيكي',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'آيس كريم الفراولة',
                    category: 'آيس كريم',
                    price: 2.8,
                    cost: 1.3,
                    stock: 35,
                    minStock: 10,
                    description: 'آيس كريم فراولة طبيعية',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            sales: [],
            customers: [
                {
                    id: 1,
                    name: 'أحمد محمد',
                    phone: '96899123456',
                    email: '<EMAIL>',
                    address: 'مسقط، سلطنة عمان',
                    membershipType: 'ذهبي',
                    discount: 10,
                    totalPurchases: 0,
                    visits: 0,
                    createdAt: new Date().toISOString()
                }
            ],
            users: [
                {
                    id: 1,
                    username: 'admin',
                    password: 'admin123',
                    name: 'المدير',
                    role: 'admin',
                    permissions: ['all'],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                }
            ],
            settings: {
                shopName: 'محل الآيس كريم الذهبي',
                shopNameEn: 'Golden Ice Cream Shop',
                shopAddress: 'مسقط، سلطنة عمان',
                shopPhone: '96824123456',
                shopEmail: '<EMAIL>',
                currency: 'OMR',
                currencySymbol: 'ر.ع',
                taxRate: 0.05,
                receiptHeader: 'محل الآيس كريم الذهبي',
                receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                receiptFontSize: 14,
                lowStockAlert: true,
                autoBackup: true,
                soundNotifications: true,
                emailReports: false,
                language: 'ar'
            }
        };
    }
}

// Helper function to write data
async function writeData(data) {
    await fs.writeFile(DATA_FILE, JSON.stringify(data, null, 2));
}

// Get all data
router.get('/data', async (req, res) => {
    try {
        const data = await readData();
        res.json(data);
    } catch (error) {
        console.error('Error reading data:', error);
        res.status(500).json({ error: 'فشل في قراءة البيانات' });
    }
});

// Save all data
router.post('/data', async (req, res) => {
    try {
        await writeData(req.body);
        res.json({ message: 'تم حفظ البيانات بنجاح' });
    } catch (error) {
        console.error('Error saving data:', error);
        res.status(500).json({ error: 'فشل في حفظ البيانات' });
    }
});

// Authentication
router.post('/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (storage) {
            // Use database storage
            const user = await storage.getUserByUsername(username);
            
            if (user && user.password === password && user.isActive) {
                // Update last login
                await storage.updateUser(user.id, { lastLogin: new Date() });
                
                // Return user without password
                const { password: _, ...userWithoutPassword } = user;
                res.json({ 
                    success: true, 
                    user: userWithoutPassword,
                    message: 'تم تسجيل الدخول بنجاح'
                });
            } else {
                res.status(401).json({ 
                    success: false, 
                    message: 'اسم المستخدم أو كلمة المرور غير صحيحة' 
                });
            }
        } else {
            // Use file storage
            const data = await readData();
            
            const user = data.users.find(u => u.username === username && u.password === password);
            
            if (user && user.isActive) {
                // Update last login
                user.lastLogin = new Date().toISOString();
                await writeData(data);
                
                // Return user without password
                const { password: _, ...userWithoutPassword } = user;
                res.json({ 
                    success: true, 
                    user: userWithoutPassword,
                    message: 'تم تسجيل الدخول بنجاح'
                });
            } else {
                res.status(401).json({ 
                    success: false, 
                    message: 'اسم المستخدم أو كلمة المرور غير صحيحة' 
                });
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'خطأ في الخادم' });
    }
});

// Products endpoints
router.get('/products', async (req, res) => {
    try {
        if (storage) {
            const products = await storage.getAllProducts();
            res.json(products);
        } else {
            const data = await readData();
            res.json(data.products.filter(p => p.isActive));
        }
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المنتجات' });
    }
});

router.post('/products', async (req, res) => {
    try {
        const data = await readData();
        const newProduct = {
            id: Math.max(...data.products.map(p => p.id), 0) + 1,
            ...req.body,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        data.products.push(newProduct);
        await writeData(data);
        res.json(newProduct);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة المنتج' });
    }
});

router.put('/products/:id', async (req, res) => {
    try {
        const data = await readData();
        const productIndex = data.products.findIndex(p => p.id === parseInt(req.params.id));
        
        if (productIndex === -1) {
            return res.status(404).json({ error: 'المنتج غير موجود' });
        }
        
        data.products[productIndex] = {
            ...data.products[productIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        res.json(data.products[productIndex]);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث المنتج' });
    }
});

router.delete('/products/:id', async (req, res) => {
    try {
        const data = await readData();
        const productIndex = data.products.findIndex(p => p.id === parseInt(req.params.id));
        
        if (productIndex === -1) {
            return res.status(404).json({ error: 'المنتج غير موجود' });
        }
        
        // Soft delete
        data.products[productIndex].isActive = false;
        data.products[productIndex].updatedAt = new Date().toISOString();
        
        await writeData(data);
        res.json({ message: 'تم حذف المنتج بنجاح' });
    } catch (error) {
        res.status(500).json({ error: 'فشل في حذف المنتج' });
    }
});

// Sales endpoints
router.get('/sales', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.sales);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المبيعات' });
    }
});

router.post('/sales', async (req, res) => {
    try {
        const data = await readData();
        const newSale = {
            id: Math.max(...data.sales.map(s => s.id), 0) + 1,
            transactionId: `TXN${String(data.sales.length + 1).padStart(6, '0')}`,
            date: new Date().toISOString(),
            ...req.body,
            status: 'completed',
            createdAt: new Date().toISOString()
        };
        
        // Update inventory
        req.body.items.forEach(item => {
            const product = data.products.find(p => p.id === item.productId);
            if (product) {
                product.stock -= item.quantity;
                product.updatedAt = new Date().toISOString();
            }
        });
        
        data.sales.push(newSale);
        await writeData(data);
        res.json(newSale);
    } catch (error) {
        res.status(500).json({ error: 'فشل في حفظ المبيعات' });
    }
});

// Customers endpoints
router.get('/customers', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.customers);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة العملاء' });
    }
});

router.post('/customers', async (req, res) => {
    try {
        const data = await readData();
        const newCustomer = {
            id: Math.max(...data.customers.map(c => c.id), 0) + 1,
            ...req.body,
            totalPurchases: 0,
            visits: 0,
            createdAt: new Date().toISOString()
        };
        
        data.customers.push(newCustomer);
        await writeData(data);
        res.json(newCustomer);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة العميل' });
    }
});

router.put('/customers/:id', async (req, res) => {
    try {
        const data = await readData();
        const customerIndex = data.customers.findIndex(c => c.id === parseInt(req.params.id));
        
        if (customerIndex === -1) {
            return res.status(404).json({ error: 'العميل غير موجود' });
        }
        
        data.customers[customerIndex] = {
            ...data.customers[customerIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        res.json(data.customers[customerIndex]);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث العميل' });
    }
});

// Users endpoints
router.get('/users', async (req, res) => {
    try {
        const data = await readData();
        // Return users without passwords
        const usersWithoutPasswords = data.users.map(({ password, ...user }) => user);
        res.json(usersWithoutPasswords);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة المستخدمين' });
    }
});

router.post('/users', async (req, res) => {
    try {
        const data = await readData();
        
        // Check if username already exists
        if (data.users.find(u => u.username === req.body.username)) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }
        
        const newUser = {
            id: Math.max(...data.users.map(u => u.id), 0) + 1,
            ...req.body,
            isActive: true,
            createdAt: new Date().toISOString(),
            lastLogin: null
        };
        
        data.users.push(newUser);
        await writeData(data);
        
        // Return user without password
        const { password: _, ...userWithoutPassword } = newUser;
        res.json(userWithoutPassword);
    } catch (error) {
        res.status(500).json({ error: 'فشل في إضافة المستخدم' });
    }
});

router.put('/users/:id', async (req, res) => {
    try {
        const data = await readData();
        const userIndex = data.users.findIndex(u => u.id === parseInt(req.params.id));
        
        if (userIndex === -1) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        data.users[userIndex] = {
            ...data.users[userIndex],
            ...req.body,
            updatedAt: new Date().toISOString()
        };
        
        await writeData(data);
        
        // Return user without password
        const { password: _, ...userWithoutPassword } = data.users[userIndex];
        res.json(userWithoutPassword);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تحديث المستخدم' });
    }
});

// Settings endpoints
router.get('/settings', async (req, res) => {
    try {
        const data = await readData();
        res.json(data.settings);
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة الإعدادات' });
    }
});

router.put('/settings', async (req, res) => {
    try {
        const data = await readData();
        data.settings = { ...data.settings, ...req.body };
        await writeData(data);
        res.json(data.settings);
    } catch (error) {
        res.status(500).json({ error: 'فشل في حفظ الإعدادات' });
    }
});

// Backup and export endpoints
router.get('/export', async (req, res) => {
    try {
        const data = await readData();
        const exportData = {
            ...data,
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename=icecreamshop_backup_${new Date().toISOString().split('T')[0]}.json`);
        res.json(exportData);
    } catch (error) {
        res.status(500).json({ error: 'فشل في تصدير البيانات' });
    }
});

router.post('/import', async (req, res) => {
    try {
        // Validate import data structure
        const importData = req.body;
        if (!importData.products || !importData.sales || !importData.customers || !importData.users || !importData.settings) {
            return res.status(400).json({ error: 'بيانات الاستيراد غير صالحة' });
        }
        
        await writeData(importData);
        res.json({ message: 'تم استيراد البيانات بنجاح' });
    } catch (error) {
        res.status(500).json({ error: 'فشل في استيراد البيانات' });
    }
});

// Analytics endpoints
router.get('/analytics/dashboard', async (req, res) => {
    try {
        if (storage) {
            const stats = await storage.getDashboardStats();
            res.json(stats);
        } else {
            const data = await readData();
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            const todaySales = data.sales.filter(sale => new Date(sale.date) >= startOfDay);
            const monthSales = data.sales.filter(sale => new Date(sale.date) >= startOfMonth);
            
            const stats = {
                todaySales: todaySales.length,
                todayRevenue: todaySales.reduce((sum, sale) => sum + sale.total, 0),
                monthSales: monthSales.length,
                monthRevenue: monthSales.reduce((sum, sale) => sum + sale.total, 0),
                totalProducts: data.products.filter(p => p.isActive).length,
                totalCustomers: data.customers.length,
                lowStockProducts: data.products.filter(p => p.stock <= p.minStock).length,
                totalRevenue: data.sales.reduce((sum, sale) => sum + sale.total, 0)
            };
            
            res.json(stats);
        }
    } catch (error) {
        res.status(500).json({ error: 'فشل في قراءة الإحصائيات' });
    }
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'خادم نظام إدارة محل الآيس كريم يعمل بنجاح',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;