<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍦 نظام إدارة محل الآيس كريم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- Loading screen -->
        <div id="loading" class="loading-screen">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحميل نظام إدارة محل الآيس كريم...</p>
        </div>

        <!-- Login Component -->
        <div id="login-component" class="d-none">
            <div class="container-fluid login-container">
                <div class="row justify-content-center align-items-center min-vh-100">
                    <div class="col-md-6 col-lg-4">
                        <div class="card shadow-lg">
                            <div class="card-body p-5">
                                <div class="text-center mb-4">
                                    <h1 class="display-4">🍦</h1>
                                    <h2 class="card-title">نظام إدارة محل الآيس كريم</h2>
                                    <p class="text-muted">مرحباً بك في نظام الإدارة المتكامل</p>
                                </div>
                                
                                <form id="login-form">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">اسم المستخدم</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="username" value="admin" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="form-control" id="password" value="admin123" required>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </button>
                                </form>
                                
                                <div class="mt-3 text-center">
                                    <small class="text-muted">
                                        المستخدم الافتراضي: admin / admin123
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Application -->
        <div id="main-app" class="d-none">
            <!-- Navigation Header -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <span class="navbar-brand">
                        <i class="fas fa-ice-cream me-2"></i>
                        نظام إدارة محل الآيس كريم
                    </span>
                    
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i>
                                <span id="current-user">المدير</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="app.logout()">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="container-fluid p-0">
                <div class="row g-0">
                    <!-- Sidebar -->
                    <div class="col-md-3 col-lg-2 sidebar">
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action active" data-component="dashboard">
                                <i class="fas fa-home me-2"></i>الرئيسية
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="sales">
                                <i class="fas fa-cash-register me-2"></i>نقطة البيع
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="products">
                                <i class="fas fa-ice-cream me-2"></i>إدارة المنتجات
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="inventory">
                                <i class="fas fa-boxes me-2"></i>إدارة المخزون
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="customers">
                                <i class="fas fa-users me-2"></i>إدارة العملاء
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="reports">
                                <i class="fas fa-chart-bar me-2"></i>التقارير
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="users">
                                <i class="fas fa-user-cog me-2"></i>إدارة المستخدمين
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" data-component="settings">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="col-md-9 col-lg-10 main-content">
                        <div id="content-area" class="p-4">
                            <!-- Dynamic content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container position-fixed top-0 end-0 p-3">
            <div id="toast-template" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong class="me-auto">إشعار</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    <!-- Toast message will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="utils/helpers.js"></script>
    <script src="data-service.js"></script>
    <script src="components/login.js"></script>
    <script src="components/main-dashboard.js"></script>
    <script src="components/sales-window.js"></script>
    <script src="components/products-window.js"></script>
    <script src="components/inventory-window.js"></script>
    <script src="components/reports-window.js"></script>
    <script src="components/customers-window.js"></script>
    <script src="components/users-window.js"></script>
    <script src="components/settings-window.js"></script>
    <script src="app.js"></script>
</body>
</html>
