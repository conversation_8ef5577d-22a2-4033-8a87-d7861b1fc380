import { 
  users, 
  products, 
  sales, 
  saleItems, 
  customers, 
  settings, 
  inventoryTransactions,
  type User, 
  type InsertUser, 
  type Product, 
  type InsertProduct,
  type Sale,
  type InsertSale,
  type SaleItem,
  type InsertSaleItem,
  type Customer,
  type InsertCustomer,
  type Setting,
  type InsertSetting,
  type InventoryTransaction,
  type InsertInventoryTransaction
} from "../shared/schema";
import { db } from "./db";
import { eq, desc, sql, and, gte, lte } from "drizzle-orm";

// Storage interface
export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(insertUser: InsertUser): Promise<User>;
  updateUser(id: number, userData: Partial<InsertUser>): Promise<User>;
  getAllUsers(): Promise<User[]>;

  // Products
  getProduct(id: number): Promise<Product | undefined>;
  getAllProducts(): Promise<Product[]>;
  createProduct(insertProduct: InsertProduct): Promise<Product>;
  updateProduct(id: number, productData: Partial<InsertProduct>): Promise<Product>;
  deleteProduct(id: number): Promise<void>;

  // Sales
  getSale(id: number): Promise<Sale | undefined>;
  getAllSales(): Promise<Sale[]>;
  getSalesByDateRange(startDate: Date, endDate: Date): Promise<Sale[]>;
  createSale(saleData: InsertSale, items: InsertSaleItem[]): Promise<Sale>;

  // Customers
  getCustomer(id: number): Promise<Customer | undefined>;
  getAllCustomers(): Promise<Customer[]>;
  createCustomer(insertCustomer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, customerData: Partial<InsertCustomer>): Promise<Customer>;

  // Settings
  getSetting(key: string): Promise<Setting | undefined>;
  getAllSettings(): Promise<Setting[]>;
  setSetting(key: string, value: string, type?: string): Promise<Setting>;

  // Inventory
  createInventoryTransaction(transaction: InsertInventoryTransaction): Promise<InventoryTransaction>;
  getInventoryTransactions(productId?: number): Promise<InventoryTransaction[]>;

  // Analytics
  getDashboardStats(): Promise<any>;
  getTopSellingProducts(limit?: number): Promise<any[]>;
  getSalesChartData(days?: number): Promise<any[]>;
}

// Database storage implementation
export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User> {
    const [user] = await db
      .update(users)
      .set({ ...userData, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(desc(users.createdAt));
  }

  async getProduct(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product || undefined;
  }

  async getAllProducts(): Promise<Product[]> {
    return await db.select().from(products).where(eq(products.isActive, true)).orderBy(desc(products.createdAt));
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const [product] = await db
      .insert(products)
      .values(insertProduct)
      .returning();
    return product;
  }

  async updateProduct(id: number, productData: Partial<InsertProduct>): Promise<Product> {
    const [product] = await db
      .update(products)
      .set({ ...productData, updatedAt: new Date() })
      .where(eq(products.id, id))
      .returning();
    return product;
  }

  async deleteProduct(id: number): Promise<void> {
    await db
      .update(products)
      .set({ isActive: false, updatedAt: new Date() })
      .where(eq(products.id, id));
  }

  async getSale(id: number): Promise<Sale | undefined> {
    const [sale] = await db.select().from(sales).where(eq(sales.id, id));
    return sale || undefined;
  }

  async getAllSales(): Promise<Sale[]> {
    return await db.select().from(sales).orderBy(desc(sales.date));
  }

  async getSalesByDateRange(startDate: Date, endDate: Date): Promise<Sale[]> {
    return await db.select()
      .from(sales)
      .where(and(
        gte(sales.date, startDate),
        lte(sales.date, endDate)
      ))
      .orderBy(desc(sales.date));
  }

  async createSale(saleData: InsertSale, items: InsertSaleItem[]): Promise<Sale> {
    return await db.transaction(async (tx) => {
      // Create sale
      const [sale] = await tx
        .insert(sales)
        .values(saleData)
        .returning();

      // Create sale items
      const saleItemsWithSaleId = items.map(item => ({
        ...item,
        saleId: sale.id
      }));

      await tx.insert(saleItems).values(saleItemsWithSaleId);

      // Update product stock and create inventory transactions
      for (const item of items) {
        // Update product stock
        await tx
          .update(products)
          .set({ 
            stock: sql`${products.stock} - ${item.quantity}`,
            updatedAt: new Date()
          })
          .where(eq(products.id, item.productId));

        // Create inventory transaction
        await tx.insert(inventoryTransactions).values({
          productId: item.productId,
          userId: saleData.userId,
          type: 'out',
          quantity: -item.quantity,
          reason: 'Sale',
          reference: sale.transactionId
        });
      }

      // Update customer statistics if customer exists
      if (saleData.customerId) {
        await tx
          .update(customers)
          .set({
            totalPurchases: sql`${customers.totalPurchases} + ${saleData.total}`,
            visits: sql`${customers.visits} + 1`,
            updatedAt: new Date()
          })
          .where(eq(customers.id, saleData.customerId));
      }

      return sale;
    });
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    const [customer] = await db.select().from(customers).where(eq(customers.id, id));
    return customer || undefined;
  }

  async getAllCustomers(): Promise<Customer[]> {
    return await db.select().from(customers).orderBy(desc(customers.createdAt));
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    const [customer] = await db
      .insert(customers)
      .values(insertCustomer)
      .returning();
    return customer;
  }

  async updateCustomer(id: number, customerData: Partial<InsertCustomer>): Promise<Customer> {
    const [customer] = await db
      .update(customers)
      .set({ ...customerData, updatedAt: new Date() })
      .where(eq(customers.id, id))
      .returning();
    return customer;
  }

  async getSetting(key: string): Promise<Setting | undefined> {
    const [setting] = await db.select().from(settings).where(eq(settings.key, key));
    return setting || undefined;
  }

  async getAllSettings(): Promise<Setting[]> {
    return await db.select().from(settings).orderBy(settings.key);
  }

  async setSetting(key: string, value: string, type: string = 'string'): Promise<Setting> {
    const existing = await this.getSetting(key);
    
    if (existing) {
      const [setting] = await db
        .update(settings)
        .set({ value, type, updatedAt: new Date() })
        .where(eq(settings.key, key))
        .returning();
      return setting;
    } else {
      const [setting] = await db
        .insert(settings)
        .values({ key, value, type })
        .returning();
      return setting;
    }
  }

  async createInventoryTransaction(transaction: InsertInventoryTransaction): Promise<InventoryTransaction> {
    const [inventoryTransaction] = await db
      .insert(inventoryTransactions)
      .values(transaction)
      .returning();
    return inventoryTransaction;
  }

  async getInventoryTransactions(productId?: number): Promise<InventoryTransaction[]> {
    const query = db.select().from(inventoryTransactions);
    
    if (productId) {
      return await query.where(eq(inventoryTransactions.productId, productId))
        .orderBy(desc(inventoryTransactions.createdAt));
    }
    
    return await query.orderBy(desc(inventoryTransactions.createdAt));
  }

  async getDashboardStats(): Promise<any> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const [todaySalesResult] = await db
      .select({ 
        count: sql<number>`count(*)::int`,
        total: sql<number>`sum(${sales.total})::numeric`
      })
      .from(sales)
      .where(gte(sales.date, startOfDay));

    const [monthSalesResult] = await db
      .select({ 
        count: sql<number>`count(*)::int`,
        total: sql<number>`sum(${sales.total})::numeric`
      })
      .from(sales)
      .where(gte(sales.date, startOfMonth));

    const [totalProductsResult] = await db
      .select({ count: sql<number>`count(*)::int` })
      .from(products)
      .where(eq(products.isActive, true));

    const [totalCustomersResult] = await db
      .select({ count: sql<number>`count(*)::int` })
      .from(customers);

    const [lowStockProductsResult] = await db
      .select({ count: sql<number>`count(*)::int` })
      .from(products)
      .where(and(
        eq(products.isActive, true),
        sql`${products.stock} <= ${products.minStock}`
      ));

    const [totalRevenueResult] = await db
      .select({ total: sql<number>`sum(${sales.total})::numeric` })
      .from(sales);

    return {
      todaySales: todaySalesResult?.count || 0,
      todayRevenue: parseFloat(String(todaySalesResult?.total || '0')),
      monthSales: monthSalesResult?.count || 0,
      monthRevenue: parseFloat(String(monthSalesResult?.total || '0')),
      totalProducts: totalProductsResult?.count || 0,
      totalCustomers: totalCustomersResult?.count || 0,
      lowStockProducts: lowStockProductsResult?.count || 0,
      totalRevenue: parseFloat(String(totalRevenueResult?.total || '0'))
    };
  }

  async getTopSellingProducts(limit: number = 5): Promise<any[]> {
    return await db
      .select({
        productId: saleItems.productId,
        name: products.name,
        totalQuantity: sql<number>`sum(${saleItems.quantity})::int`,
        totalRevenue: sql<number>`sum(${saleItems.total})::numeric`
      })
      .from(saleItems)
      .leftJoin(products, eq(saleItems.productId, products.id))
      .groupBy(saleItems.productId, products.name)
      .orderBy(sql`sum(${saleItems.quantity}) DESC`)
      .limit(limit);
  }

  async getSalesChartData(days: number = 7): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return await db
      .select({
        date: sql<string>`date(${sales.date})`,
        total: sql<number>`sum(${sales.total})::numeric`,
        count: sql<number>`count(*)::int`
      })
      .from(sales)
      .where(gte(sales.date, startDate))
      .groupBy(sql`date(${sales.date})`)
      .orderBy(sql`date(${sales.date})`);
  }
}

export const storage = new DatabaseStorage();