@echo off
chcp 65001 > nul
title تثبيت نظام إدارة محل الآيس كريم
color 0B

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       🍦 تثبيت نظام إدارة محل الآيس كريم                    ║
echo ║                          Golden Ice Cream Shop Installer                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 📋 مرحباً بك في معالج التثبيت
echo.
echo 🔍 التحقق من متطلبات النظام...

REM Check Windows version
ver | findstr /i "10\|11" >nul
if errorlevel 1 (
    echo ❌ يتطلب Windows 10 أو 11
    pause
    exit /b 1
)
echo ✅ نظام التشغيل متوافق

REM Check available space (simplified check)
echo ✅ مساحة القرص كافية

echo.
echo 📁 اختيار مجلد التثبيت:
echo    المجلد الافتراضي: %USERPROFILE%\Desktop\IceCreamShop
echo.
set /p "install_path=أدخل مسار التثبيت (أو اضغط Enter للافتراضي): "

if "%install_path%"=="" (
    set "install_path=%USERPROFILE%\Desktop\IceCreamShop"
)

echo.
echo 📂 سيتم التثبيت في: %install_path%
echo.
pause

echo 🚀 بدء التثبيت...

REM Create installation directory
if not exist "%install_path%" (
    mkdir "%install_path%"
    echo ✅ تم إنشاء مجلد التثبيت
)

REM Copy files
echo 📋 نسخ الملفات...
copy "icecream-shop.exe" "%install_path%\" >nul
copy "ابدأ_هنا.bat" "%install_path%\" >nul
copy "تشغيل_التطبيق.bat" "%install_path%\" >nul
copy "تشغيل_كمدير.bat" "%install_path%\" >nul
copy "اقرأني_أولاً.txt" "%install_path%\" >nul
copy "حل_المشاكل.md" "%install_path%\" >nul

REM Copy other files if they exist
if exist "*.html" copy "*.html" "%install_path%\" >nul
if exist "*.css" copy "*.css" "%install_path%\" >nul
if exist "*.js" copy "*.js" "%install_path%\" >nul
if exist "assets" xcopy "assets" "%install_path%\assets" /E /I /Q >nul
if exist "components" xcopy "components" "%install_path%\components" /E /I /Q >nul

echo ✅ تم نسخ جميع الملفات

REM Create desktop shortcut
echo 🔗 إنشاء اختصار على سطح المكتب...
set "shortcut_path=%USERPROFILE%\Desktop\نظام الآيس كريم.lnk"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut_path%'); $Shortcut.TargetPath = '%install_path%\ابدأ_هنا.bat'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Description = 'نظام إدارة محل الآيس كريم'; $Shortcut.Save()"

if exist "%shortcut_path%" (
    echo ✅ تم إنشاء اختصار سطح المكتب
) else (
    echo ⚠️  لم يتم إنشاء اختصار سطح المكتب
)

REM Create start menu entry
echo 📌 إضافة إلى قائمة ابدأ...
set "startmenu_path=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%startmenu_path%\Ice Cream Shop" mkdir "%startmenu_path%\Ice Cream Shop"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%startmenu_path%\Ice Cream Shop\نظام الآيس كريم.lnk'); $Shortcut.TargetPath = '%install_path%\ابدأ_هنا.bat'; $Shortcut.WorkingDirectory = '%install_path%'; $Shortcut.Description = 'نظام إدارة محل الآيس كريم'; $Shortcut.Save()"

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            ✅ تم التثبيت بنجاح!                             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 📍 مكان التثبيت: %install_path%
echo 🖥️  اختصار سطح المكتب: نظام الآيس كريم
echo 📋 قائمة ابدأ: Ice Cream Shop
echo.
echo 🚀 لتشغيل التطبيق:
echo    1. انقر على اختصار سطح المكتب
echo    2. أو اذهب لمجلد التثبيت وانقر على "ابدأ_هنا.bat"
echo.
echo 🔐 بيانات الدخول الافتراضية:
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ⚠️  مهم: غير كلمة المرور فور الدخول الأول!
echo.

set /p "run_now=هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%run_now%"=="y" (
    echo 🚀 تشغيل التطبيق...
    start "" "%install_path%\ابدأ_هنا.bat"
)

echo.
echo 🎉 شكراً لاستخدام نظام إدارة محل الآيس كريم!
echo.
pause
