# نظام إدارة محل الآيس كريم الذهبي
## Golden Ice Cream Shop Management System

نظام شامل لإدارة محلات الآيس كريم باللغة العربية مع دعم الريال العماني

## 🌟 المميزات الرئيسية

### 📊 لوحة التحكم
- إحصائيات المبيعات اليومية والشهرية
- رسوم بيانية للمبيعات والأرباح
- تنبيهات نفاد المخزون
- ملخص الأداء العام

### 🛒 نقطة البيع (POS)
- واجهة سهلة وسريعة للمبيعات
- دعم الباركود والبحث السريع
- حساب الضرائب والخصومات
- طباعة الفواتير باللغة العربية

### 📦 إدارة المخزون
- تتبع مستويات المخزون
- تنبيهات نفاد المخزون
- تسجيل حركات المخزون
- تقارير المخزون المفصلة

### 👥 إدارة العملاء
- قاعدة بيانات العملاء
- برنامج العضوية والولاء
- تتبع مشتريات العملاء
- خصومات حسب نوع العضوية

### 📈 التقارير والتحليلات
- تقارير المبيعات والأرباح
- تحليل أداء المنتجات
- تقارير العملاء والعضويات
- تقارير المخزون والتكاليف

### 👨‍💼 إدارة المستخدمين
- نظام أذونات متقدم
- أدوار مختلفة (مدير، كاشير)
- تسجيل الدخول الآمن
- تتبع نشاط المستخدمين

### ⚙️ الإعدادات
- إعدادات المتجر والشركة
- تخصيص الفواتير
- إعدادات العملة والضرائب
- نسخ احتياطية للبيانات

## 🚀 طريقة التشغيل

### متطلبات التشغيل
- Node.js 16.0 أو أحدث
- متصفح ويب حديث
- نظام التشغيل: Windows, macOS, Linux

### التشغيل على Windows
1. انقر نقراً مزدوجاً على ملف `start-desktop.bat`
2. انتظر حتى يتم تحميل التطبيق
3. سيتم فتح المتصفح تلقائياً

### التشغيل على macOS
1. انقر نقراً مزدوجاً على ملف `start-desktop.command`
2. إذا ظهر تحذير أمني، اذهب إلى تفضيلات النظام > الأمان والخصوصية
3. انقر "فتح على أي حال"

### التشغيل على Linux
1. افتح الطرفية (Terminal)
2. اذهب إلى مجلد التطبيق
3. شغل الأمر: `./start-desktop.sh`

### التشغيل اليدوي
```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
node desktop-launcher.js
```

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول

## 🌐 الوصول للتطبيق

- **المحلي:** http://localhost:3000
- **الشبكة:** http://[عنوان_IP]:3000

## 📱 الدعم والتوافق

### المتصفحات المدعومة
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### دقة الشاشة المناسبة
- الحد الأدنى: 1024x768
- الموصى به: 1366x768 أو أعلى
- دعم كامل للشاشات اللمسية

## 💾 إدارة البيانات

### النسخ الاحتياطية
- نسخ احتياطية تلقائية يومية
- تصدير البيانات بصيغة JSON
- استيراد البيانات من النسخ الاحتياطية

### ملف البيانات
- يتم حفظ البيانات في ملف `icecreamshop_data.json`
- يمكن نسخ هذا الملف كنسخة احتياطية
- يتم إنشاء البيانات التجريبية تلقائياً في أول تشغيل

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### التطبيق لا يعمل
- تأكد من تثبيت Node.js
- تحقق من إغلاق التطبيقات الأخرى التي تستخدم المنفذ 3000

#### لا يمكن الوصول من الأجهزة الأخرى
- تأكد من إعدادات الجدار الناري
- تحقق من عنوان IP الخاص بالجهاز

#### فقدان البيانات
- تحقق من وجود ملف `icecreamshop_data.json`
- استخدم النسخة الاحتياطية إذا كانت متوفرة

## 📧 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- تحقق من ملف log الخاص بالتطبيق
- راسلنا مع تفاصيل المشكلة

## 📄 الترخيص

هذا البرنامج مجاني للاستخدام الشخصي والتجاري.

---

## English Version

# Golden Ice Cream Shop Management System

A comprehensive ice cream shop management system with Arabic interface and Omani Rial support.

## Features
- Point of Sale (POS) system
- Inventory management
- Customer management
- Sales analytics and reports
- User management with roles
- Arabic RTL interface
- Omani Rial currency support

## Quick Start
1. Ensure Node.js is installed
2. Run the appropriate launcher for your OS
3. Access the application at http://localhost:3000
4. Default login: admin / admin123

## System Requirements
- Node.js 16.0+
- Modern web browser
- Windows, macOS, or Linux

For detailed instructions, please refer to the Arabic documentation above.