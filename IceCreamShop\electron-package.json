{"name": "golden-icecream-shop", "version": "1.0.0", "description": "نظام إدارة محل الآيس كريم الذهبي - Golden Ice Cream Shop Management System", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "server": "node server.js"}, "keywords": ["ice cream", "pos", "management", "arabic", "electron", "desktop"], "author": {"name": "Ice Cream Shop Management", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron": "^37.2.0", "express": "^5.1.0"}, "devDependencies": {"electron-builder": "^26.0.12"}, "build": {"appId": "com.icecreamshop.golden", "productName": "نظام إدارة محل الآيس كريم", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "server-routes.js", "index.html", "styles.css", "app.js", "data-service.js", "components/**/*", "utils/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico"}}}