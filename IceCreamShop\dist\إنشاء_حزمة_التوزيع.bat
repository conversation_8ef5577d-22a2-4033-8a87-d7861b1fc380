@echo off
chcp 65001 > nul
title إنشاء حزمة التوزيع - نظام إدارة محل الآيس كريم
color 0E

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    📦 إنشاء حزمة التوزيع النهائية                          ║
echo ║                          Golden Ice Cream Shop                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من الملفات المطلوبة...

REM Check if main exe exists
if not exist "icecream-shop.exe" (
    echo ❌ خطأ: الملف التنفيذي الرئيسي غير موجود
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود (حجم: 39 MB)

REM Create distribution package name with date
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "datestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%"
set "package_name=IceCreamShop_v1.0_%datestamp%"

echo.
echo 📦 إنشاء حزمة التوزيع: %package_name%
echo.

REM Create package directory
if exist "%package_name%" rmdir /s /q "%package_name%"
mkdir "%package_name%"

echo 📋 نسخ الملفات الأساسية...

REM Copy main executable
copy "icecream-shop.exe" "%package_name%\" >nul
echo ✅ الملف التنفيذي الرئيسي

REM Copy launcher files
copy "ابدأ_هنا.bat" "%package_name%\" >nul
copy "تشغيل_التطبيق.bat" "%package_name%\" >nul
copy "تشغيل_كمدير.bat" "%package_name%\" >nul
echo ✅ ملفات التشغيل

REM Copy installer files
copy "تثبيت_التطبيق.bat" "%package_name%\" >nul
copy "إلغاء_التثبيت.bat" "%package_name%\" >nul
echo ✅ ملفات التثبيت

REM Copy documentation
copy "اقرأني_أولاً.txt" "%package_name%\" >nul
copy "حل_المشاكل.md" "%package_name%\" >nul
copy "تعليمات_التشغيل.md" "%package_name%\" >nul
copy "دليل_التثبيت.md" "%package_name%\" >nul
echo ✅ ملفات التوثيق

REM Copy web files if they exist
if exist "*.html" (
    copy "*.html" "%package_name%\" >nul
    echo ✅ ملفات HTML
)
if exist "*.css" (
    copy "*.css" "%package_name%\" >nul
    echo ✅ ملفات CSS
)
if exist "*.js" (
    copy "*.js" "%package_name%\" >nul
    echo ✅ ملفات JavaScript
)

REM Copy directories
if exist "assets" (
    xcopy "assets" "%package_name%\assets" /E /I /Q >nul
    echo ✅ مجلد الأصول
)
if exist "components" (
    xcopy "components" "%package_name%\components" /E /I /Q >nul
    echo ✅ مجلد المكونات
)
if exist "utils" (
    xcopy "utils" "%package_name%\utils" /E /I /Q >nul
    echo ✅ مجلد الأدوات
)

REM Create a version info file
echo 🍦 نظام إدارة محل الآيس كريم الذهبي > "%package_name%\VERSION.txt"
echo Golden Ice Cream Shop Management System >> "%package_name%\VERSION.txt"
echo. >> "%package_name%\VERSION.txt"
echo الإصدار: 1.0 >> "%package_name%\VERSION.txt"
echo تاريخ الإنشاء: %datestamp% >> "%package_name%\VERSION.txt"
echo حجم الملف التنفيذي: 39 ميجابايت >> "%package_name%\VERSION.txt"
echo. >> "%package_name%\VERSION.txt"
echo متطلبات النظام: >> "%package_name%\VERSION.txt"
echo - Windows 10/11 (64-bit) >> "%package_name%\VERSION.txt"
echo - 4 جيجابايت رام أو أكثر >> "%package_name%\VERSION.txt"
echo - 100 ميجابايت مساحة فارغة >> "%package_name%\VERSION.txt"
echo. >> "%package_name%\VERSION.txt"
echo للتشغيل السريع: انقر على ابدأ_هنا.bat >> "%package_name%\VERSION.txt"

echo ✅ ملف معلومات الإصدار

REM Create a simple README for the package
echo 🚀 تشغيل سريع: > "%package_name%\README.txt"
echo ============== >> "%package_name%\README.txt"
echo. >> "%package_name%\README.txt"
echo 1. انقر نقراً مزدوجاً على: ابدأ_هنا.bat >> "%package_name%\README.txt"
echo 2. انتظر فتح المتصفح تلقائياً >> "%package_name%\README.txt"
echo 3. استخدم بيانات الدخول: >> "%package_name%\README.txt"
echo    المستخدم: admin >> "%package_name%\README.txt"
echo    كلمة المرور: admin123 >> "%package_name%\README.txt"
echo. >> "%package_name%\README.txt"
echo للمساعدة: اقرأ ملف "حل_المشاكل.md" >> "%package_name%\README.txt"

echo ✅ ملف README

echo.
echo 📊 إحصائيات الحزمة:
echo ==================

REM Count files
for /f %%i in ('dir /b "%package_name%\*.*" ^| find /c /v ""') do set file_count=%%i
echo 📁 عدد الملفات: %file_count%

REM Calculate total size (simplified)
for /f "tokens=3" %%i in ('dir "%package_name%" ^| find "File(s)"') do set total_size=%%i
echo 💾 الحجم الإجمالي: %total_size% بايت

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        ✅ تم إنشاء حزمة التوزيع بنجاح!                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 📦 اسم الحزمة: %package_name%
echo 📍 المكان: %cd%\%package_name%
echo.
echo 🎯 الحزمة جاهزة للتوزيع!
echo.
echo 📋 يمكنك الآن:
echo    1. ضغط المجلد إلى ملف ZIP
echo    2. توزيع الحزمة على أي جهاز Windows
echo    3. تشغيل التطبيق بنقرة واحدة
echo.

set /p "open_folder=هل تريد فتح مجلد الحزمة؟ (y/n): "
if /i "%open_folder%"=="y" (
    explorer "%package_name%"
)

echo.
echo 🎉 شكراً لاستخدام نظام إدارة محل الآيس كريم!
echo.
pause
