const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function createTables() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 Creating database tables...');
    
    // Create tables
    await client.query(`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        price DECIMAL(10,3) NOT NULL,
        cost DECIMAL(10,3) NOT NULL,
        stock INTEGER NOT NULL DEFAULT 0,
        min_stock INTEGER NOT NULL DEFAULT 0,
        description TEXT,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(100),
        address TEXT,
        membership_type VARCHAR(20) NOT NULL DEFAULT 'عادي',
        discount INTEGER NOT NULL DEFAULT 0,
        total_purchases DECIMAL(10,3) NOT NULL DEFAULT 0,
        visits INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        name TEXT NOT NULL,
        role VARCHAR(20) NOT NULL DEFAULT 'cashier',
        permissions JSON,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS sales (
        id SERIAL PRIMARY KEY,
        transaction_id VARCHAR(50) NOT NULL UNIQUE,
        customer_id INTEGER REFERENCES customers(id),
        user_id INTEGER REFERENCES users(id),
        subtotal DECIMAL(10,3) NOT NULL,
        discount DECIMAL(10,3) NOT NULL DEFAULT 0,
        tax DECIMAL(10,3) NOT NULL DEFAULT 0,
        total DECIMAL(10,3) NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'completed',
        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS sale_items (
        id SERIAL PRIMARY KEY,
        sale_id INTEGER REFERENCES sales(id) NOT NULL,
        product_id INTEGER REFERENCES products(id) NOT NULL,
        quantity INTEGER NOT NULL,
        price DECIMAL(10,3) NOT NULL,
        total DECIMAL(10,3) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        key VARCHAR(100) NOT NULL UNIQUE,
        value TEXT NOT NULL,
        type VARCHAR(20) NOT NULL DEFAULT 'string',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS inventory_transactions (
        id SERIAL PRIMARY KEY,
        product_id INTEGER REFERENCES products(id) NOT NULL,
        user_id INTEGER REFERENCES users(id),
        type VARCHAR(20) NOT NULL,
        quantity INTEGER NOT NULL,
        reason TEXT,
        reference VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('✅ Database tables created successfully!');
    
    // Check if admin user exists
    const adminUser = await client.query('SELECT id FROM users WHERE username = $1', ['admin']);
    
    if (adminUser.rows.length === 0) {
      console.log('🔄 Creating default data...');
      
      // Create admin user
      await client.query(`
        INSERT INTO users (username, password, name, role, permissions, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, ['admin', 'admin123', 'المدير', 'admin', JSON.stringify(['all']), true]);
      
      // Create default products
      await client.query(`
        INSERT INTO products (name, category, price, cost, stock, min_stock, description, is_active)
        VALUES 
          ('آيس كريم الفانيليا', 'آيس كريم', 2.500, 1.200, 50, 10, 'آيس كريم فانيليا طبيعي', true),
          ('آيس كريم الشوكولاتة', 'آيس كريم', 3.000, 1.500, 40, 10, 'آيس كريم شوكولاتة بلجيكي', true),
          ('آيس كريم الفراولة', 'آيس كريم', 2.800, 1.300, 35, 10, 'آيس كريم فراولة طبيعية', true),
          ('آيس كريم المانجو', 'آيس كريم', 3.200, 1.600, 30, 10, 'آيس كريم مانجو استوائي', true),
          ('آيس كريم الفستق', 'آيس كريم', 3.500, 1.800, 25, 10, 'آيس كريم فستق حلبي', true)
      `);
      
      // Create default customer
      await client.query(`
        INSERT INTO customers (name, phone, email, address, membership_type, discount, total_purchases, visits)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, ['أحمد محمد', '96899123456', '<EMAIL>', 'مسقط، سلطنة عمان', 'ذهبي', 10, 0, 0]);
      
      // Create default settings
      const defaultSettings = [
        ['shopName', 'محل الآيس كريم الذهبي', 'string'],
        ['shopNameEn', 'Golden Ice Cream Shop', 'string'],
        ['shopAddress', 'مسقط، سلطنة عمان', 'string'],
        ['shopPhone', '96824123456', 'string'],
        ['shopEmail', '<EMAIL>', 'string'],
        ['currency', 'OMR', 'string'],
        ['currencySymbol', 'ر.ع', 'string'],
        ['taxRate', '0.05', 'number'],
        ['receiptHeader', 'محل الآيس كريم الذهبي', 'string'],
        ['receiptFooter', 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى', 'string'],
        ['receiptFontSize', '14', 'number'],
        ['lowStockAlert', 'true', 'boolean'],
        ['autoBackup', 'true', 'boolean'],
        ['soundNotifications', 'true', 'boolean'],
        ['emailReports', 'false', 'boolean'],
        ['language', 'ar', 'string']
      ];
      
      for (const [key, value, type] of defaultSettings) {
        await client.query(`
          INSERT INTO settings (key, value, type)
          VALUES ($1, $2, $3)
        `, [key, value, type]);
      }
      
      console.log('✅ Default data created successfully!');
    } else {
      console.log('✅ Database already has data');
    }
    
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

createTables()
  .then(() => {
    console.log('🎉 Database migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });