# نظام إدارة محل الآيس كريم - ملف PowerShell للتشغيل
# Golden Ice Cream Shop Management System - PowerShell Launcher

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "نظام إدارة محل الآيس كريم - Golden Ice Cream Shop"

# Clear screen and show banner
Clear-Host
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                       🍦 نظام إدارة محل الآيس كريم                          ║" -ForegroundColor Cyan
Write-Host "║                          Golden Ice Cream Shop                               ║" -ForegroundColor Cyan
Write-Host "║                             الإصدار 1.0                                    ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Change to script directory
Set-Location -Path $PSScriptRoot

# Check if exe file exists
$exePath = Join-Path $PSScriptRoot "icecream-shop.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "❌ خطأ: لم يتم العثور على الملف التنفيذي" -ForegroundColor Red
    Write-Host "📁 تأكد من وجود الملف: icecream-shop.exe" -ForegroundColor Yellow
    Write-Host "📍 في نفس مجلد هذا الملف" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ تم العثور على الملف التنفيذي" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 بدء تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "⏳ يرجى الانتظار..." -ForegroundColor Yellow
Write-Host ""

try {
    # Start the application
    $process = Start-Process -FilePath $exePath -PassThru -WindowStyle Normal
    
    # Wait a moment
    Start-Sleep -Seconds 3
    
    if ($process -and !$process.HasExited) {
        Write-Host "✅ تم تشغيل التطبيق بنجاح!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🌐 سيتم فتح المتصفح تلقائياً على العنوان:" -ForegroundColor Cyan
        Write-Host "   http://localhost:3000" -ForegroundColor White
        Write-Host ""
        Write-Host "🔐 بيانات الدخول الافتراضية:" -ForegroundColor Cyan
        Write-Host "   اسم المستخدم: admin" -ForegroundColor White
        Write-Host "   كلمة المرور: admin123" -ForegroundColor White
        Write-Host ""
        Write-Host "⚠️  مهم: لا تغلق هذه النافذة أثناء استخدام التطبيق" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "🛑 لإيقاف التطبيق: اضغط أي مفتاح" -ForegroundColor Red
        
        # Wait for user input to stop
        Read-Host ""
        
        # Stop the process
        if (!$process.HasExited) {
            $process.Kill()
            Write-Host "🛑 تم إيقاف التطبيق" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ فشل في تشغيل التطبيق" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
