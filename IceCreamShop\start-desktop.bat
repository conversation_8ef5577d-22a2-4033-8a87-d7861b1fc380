@echo off
chcp 65001 > nul
title نظام إدارة محل الآيس كريم

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       🍦 نظام إدارة محل الآيس كريم                          ║
echo ║                          Golden Ice Cream Shop                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 بدء تشغيل التطبيق...
echo 📋 التحقق من متطلبات النظام...

REM Check if Node.js is installed
where node >nul 2>nul
if errorlevel 1 (
    echo.
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo 📥 يرجى تنزيل وتثبيت Node.js من الرابط التالي:
    echo 🌐 https://nodejs.org/ar/download/
    echo.
    echo 💡 بعد التثبيت، أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

REM Check if npm dependencies are installed
if not exist node_modules (
    echo 📦 تثبيت مكتبات التطبيق...
    call npm install
    if errorlevel 1 (
        echo.
        echo ❌ فشل في تثبيت المكتبات
        echo 🔧 يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
)

echo.
echo 🎯 بدء تشغيل الخادم...
echo.

REM Start the desktop application
node desktop-launcher.js

echo.
echo 🛑 تم إيقاف التطبيق
echo.
pause