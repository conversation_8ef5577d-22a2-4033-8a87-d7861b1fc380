const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  
  // Listen to main process events
  onClearData: (callback) => ipcRenderer.on('clear-data', callback),
  onImportData: (callback) => ipcRenderer.on('import-data', callback),
  onSaveData: (callback) => ipcRenderer.on('save-data', callback),
  onExportData: (callback) => ipcRenderer.on('export-data', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Platform detection
contextBridge.exposeInMainWorld('platform', {
  isElectron: true,
  isDesktop: true,
  isWeb: false,
  platform: process.platform
});