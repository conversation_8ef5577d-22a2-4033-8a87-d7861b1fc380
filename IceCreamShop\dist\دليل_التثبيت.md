# دليل تثبيت نظام إدارة محل الآيس كريم الذهبي

## 📋 متطلبات النظام

### الحد الأدنى للمتطلبات:
- **نظام التشغيل**: Windows 10/11، macOS 10.14+، أو Linux Ubuntu 18.04+
- **المعالج**: Intel Core i3 أو AMD Ryzen 3 (أو ما يعادلهما)
- **الذاكرة**: 4 جيجابايت رام
- **مساحة القرص الصلب**: 500 ميجابايت
- **الشبكة**: اتصال إنترنت للتثبيت الأولي فقط

### المتطلبات الموصى بها:
- **المعالج**: Intel Core i5 أو AMD Ryzen 5 (أو أحدث)
- **الذاكرة**: 8 جيجابايت رام أو أكثر
- **مساحة القرص الصلب**: 2 جيجابايت أو أكثر
- **دقة الشاشة**: 1366x768 أو أعلى (الموصى به: 1920x1080)

---

## 🛠️ خطوات التثبيت

### الخطوة 1: تنزيل وتثبيت Node.js

#### على Windows:
1. اذهب إلى الموقع الرسمي: https://nodejs.org/ar
2. انقر على زر "تنزيل" للإصدار LTS (النسخة المستقرة)
3. شغل ملف التثبيت `.msi` الذي تم تنزيله
4. اتبع معالج التثبيت (اترك جميع الإعدادات الافتراضية)
5. أعد تشغيل الكمبيوتر بعد اكتمال التثبيت

#### على macOS:
1. اذهب إلى الموقع الرسمي: https://nodejs.org/ar
2. انقر على زر "تنزيل" للإصدار LTS
3. شغل ملف التثبيت `.pkg` الذي تم تنزيله
4. اتبع معالج التثبيت
5. أدخل كلمة مرور المدير عند الطلب

#### على Linux (Ubuntu/Debian):
```bash
# تحديث قائمة الحزم
sudo apt update

# تثبيت Node.js و npm
sudo apt install nodejs npm

# تحقق من الإصدار
node --version
npm --version
```

### الخطوة 2: التحقق من تثبيت Node.js

1. افتح موجه الأوامر (Command Prompt) أو Terminal
2. اكتب الأمر التالي للتحقق من الإصدار:
```bash
node --version
```
3. يجب أن ترى رقم الإصدار (مثل: v18.17.0 أو أحدث)

---

## 📦 تثبيت التطبيق

### الطريقة الأولى: التنزيل المباشر

#### الخطوة 1: تنزيل ملفات التطبيق
1. قم بتنزيل ملف التطبيق المضغوط من الرابط المقدم
2. فك ضغط الملف في مجلد منفصل (مثل: `C:\IceCreamShop` على Windows أو `~/IceCreamShop` على Mac/Linux)

#### الخطوة 2: تثبيت المكتبات المطلوبة
1. افتح موجه الأوامر أو Terminal
2. انتقل إلى مجلد التطبيق:
```bash
cd C:\IceCreamShop
# أو على Mac/Linux:
cd ~/IceCreamShop
```
3. شغل أمر تثبيت المكتبات:
```bash
npm install
```
4. انتظر حتى اكتمال التثبيت (قد يستغرق 2-5 دقائق)

### الطريقة الثانية: استنساخ من GitHub (للمطورين)

```bash
# استنساخ المستودع
git clone [رابط المستودع]

# الانتقال إلى المجلد
cd ice-cream-shop

# تثبيت المكتبات
npm install
```

---

## 🚀 تشغيل التطبيق

### تشغيل تلقائي (موصى به):

#### على Windows:
1. انقر نقراً مزدوجاً على ملف `start-desktop.bat`
2. سيظهر نافذة سوداء (موجه الأوامر) - لا تغلقها
3. سيتم فتح المتصفح تلقائياً وعرض التطبيق

#### على macOS:
1. انقر نقراً مزدوجاً على ملف `start-desktop.command`
2. إذا ظهر تحذير أمني:
   - اذهب إلى تفضيلات النظام > الأمان والخصوصية
   - انقر "فتح على أي حال"
3. سيتم فتح المتصفح تلقائياً

#### على Linux:
1. افتح Terminal في مجلد التطبيق
2. شغل الأمر:
```bash
./start-desktop.sh
```
3. أو انقر نقراً مزدوجاً على الملف إذا كان مدعوماً

### تشغيل يدوي:

1. افتح موجه الأوامر أو Terminal في مجلد التطبيق
2. شغل الأمر:
```bash
node desktop-launcher.js
```
3. افتح المتصفح واذهب إلى: http://localhost:3000

---

## 🔐 تسجيل الدخول الأول

بعد فتح التطبيق في المتصفح:

1. ستظهر شاشة تسجيل الدخول
2. استخدم بيانات الدخول الافتراضية:
   - **اسم المستخدم**: `admin`
   - **كلمة المرور**: `admin123`
3. انقر "تسجيل الدخول"

⚠️ **مهم جداً**: قم بتغيير كلمة المرور فوراً بعد أول تسجيل دخول:
- اذهب إلى "إدارة المستخدمين"
- انقر على المستخدم "admin"
- اختر "تغيير كلمة المرور"

---

## ⚙️ إعداد النظام الأولي

### 1. إعدادات المتجر:
- اذهب إلى "الإعدادات" من القائمة الجانبية
- أدخل معلومات متجرك:
  - اسم المتجر
  - العنوان
  - رقم الهاتف
  - البريد الإلكتروني

### 2. إضافة المنتجات:
- اذهب إلى "إدارة المنتجات"
- انقر "إضافة منتج جديد"
- أدخل بيانات منتجاتك (الاسم، السعر، الكمية، إلخ)

### 3. إضافة العملاء:
- اذهب إلى "إدارة العملاء"
- انقر "إضافة عميل جديد"
- أدخل بيانات عملائك

### 4. إنشاء مستخدمين إضافيين:
- اذهب إلى "إدارة المستخدمين"
- انقر "إضافة مستخدم جديد"
- اختر نوع المستخدم (مدير أو كاشير)

---

## 💾 النسخ الاحتياطية

### نسخ احتياطية تلقائية:
- يتم حفظ البيانات تلقائياً في قاعدة البيانات
- يمكن تصدير نسخة احتياطية من "الإعدادات" > "النسخ الاحتياطية"

### نسخ احتياطية يدوية:
1. انسخ مجلد التطبيق بالكامل إلى مكان آمن
2. أو صدّر البيانات من داخل التطبيق بصيغة JSON

---

## 🌐 الوصول من أجهزة أخرى (اختياري)

لاستخدام التطبيق من أجهزة أخرى في نفس الشبكة:

### 1. معرفة عنوان IP:

#### على Windows:
```cmd
ipconfig
```
ابحث عن "IPv4 Address"

#### على Mac/Linux:
```bash
ifconfig
```
أو
```bash
ip addr show
```

### 2. الوصول من الأجهزة الأخرى:
- افتح المتصفح في الجهاز الآخر
- اكتب العنوان: `http://[عنوان_IP]:3000`
- مثال: `http://*************:3000`

---

## 🔧 حل المشاكل الشائعة

### المشكلة: "Node.js غير موجود"
**الحل**: تأكد من تثبيت Node.js وإعادة تشغيل الكمبيوتر

### المشكلة: "المنفذ 3000 مستخدم"
**الحل**: 
- أغلق أي تطبيق آخر يستخدم نفس المنفذ
- أو أعد تشغيل الكمبيوتر

### المشكلة: "فشل في تثبيت المكتبات"
**الحل**:
- تحقق من اتصال الإنترنت
- شغل موجه الأوامر كمدير (Run as Administrator)
- حاول مرة أخرى:
```bash
npm install --force
```

### المشكلة: صفحة فارغة في المتصفح
**الحل**:
- تأكد من أن التطبيق يعمل (انظر إلى نافذة موجه الأوامر)
- جرب عنوان مختلف: `http://127.0.0.1:3000`
- امسح ذاكرة التخزين المؤقت للمتصفح

### المشكلة: لا يمكن الوصول من أجهزة أخرى
**الحل**:
- تحقق من إعدادات الجدار الناري (Firewall)
- تأكد من أن الأجهزة في نفس الشبكة
- جرب إيقاف الجدار الناري مؤقتاً للاختبار

---

## 📞 الدعم الفني

### قبل طلب المساعدة:
1. تأكد من تثبيت Node.js بشكل صحيح
2. تحقق من رسائل الخطأ في نافذة موجه الأوامر
3. جرب إعادة تشغيل التطبيق

### طلب المساعدة:
- اذكر نظام التشغيل الذي تستخدمه
- ارفق صورة من رسالة الخطأ إن وجدت
- اذكر الخطوة التي فشلت فيها

---

## 🎯 نصائح للاستخدام الأمثل

1. **النسخ الاحتياطية**: اعمل نسخة احتياطية أسبوعياً على الأقل
2. **كلمات المرور**: استخدم كلمات مرور قوية ومعقدة
3. **التحديثات**: تحقق من التحديثات بانتظام
4. **الأداء**: أغلق البرامج غير الضرورية لتحسين الأداء
5. **الشاشة**: استخدم دقة شاشة 1920x1080 أو أعلى للحصول على أفضل تجربة

---

## ✅ قائمة مراجعة التثبيت

- [ ] تثبيت Node.js
- [ ] تنزيل ملفات التطبيق
- [ ] تثبيت مكتبات النظام (`npm install`)
- [ ] تشغيل التطبيق لأول مرة
- [ ] تسجيل الدخول بالبيانات الافتراضية
- [ ] تغيير كلمة مرور المدير
- [ ] إعداد معلومات المتجر
- [ ] إضافة المنتجات الأولى
- [ ] إنشاء نسخة احتياطية
- [ ] اختبار جميع الوظائف الأساسية

---

*تم إعداد هذا الدليل لضمان تثبيت سهل وآمن لنظام إدارة محل الآيس كريم الذهبي*