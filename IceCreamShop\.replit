modules = ["nodejs-20", "postgresql-16"]

[nix]
channel = "stable-24_05"
packages = ["glib", "gtk3", "nss", "alsa-lib", "pango", "atk", "cairo", "gdk-pixbuf"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Ice Cream Shop Server"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Electron Desktop App"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Desktop Application"

[[workflows.workflow]]
name = "Ice Cream Shop Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm init -y && npm install express && node server.js"
waitForPort = 5000

[[workflows.workflow]]
name = "Electron Desktop App"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "electron ."

[[workflows.workflow]]
name = "Desktop Application"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "node desktop-launcher.js"
waitForPort = 3000

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 5000
externalPort = 80
