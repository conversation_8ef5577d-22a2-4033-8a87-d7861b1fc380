#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    color=$1
    shift
    echo -e "${color}$@${NC}"
}

# Function to print banner
print_banner() {
    echo
    print_color $BLUE "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_color $BLUE "║                       🍦 نظام إدارة محل الآيس كريم                          ║"
    print_color $BLUE "║                          Golden Ice Cream Shop                               ║"
    print_color $BLUE "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main script
main() {
    clear
    print_banner
    
    print_color $YELLOW "🚀 بدء تشغيل التطبيق..."
    print_color $YELLOW "📋 التحقق من متطلبات النظام..."
    
    # Check if Node.js is installed
    if ! command_exists node; then
        echo
        print_color $RED "❌ Node.js غير مثبت على النظام"
        echo
        print_color $YELLOW "📥 يرجى تنزيل وتثبيت Node.js من الرابط التالي:"
        print_color $BLUE "🌐 https://nodejs.org/ar/download/"
        echo
        print_color $YELLOW "💡 بعد التثبيت، أعد تشغيل هذا الملف"
        echo
        read -p "اضغط Enter للمتابعة..."
        exit 1
    fi
    
    print_color $GREEN "✅ Node.js مثبت بنجاح"
    echo
    
    # Check if npm dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_color $YELLOW "📦 تثبيت مكتبات التطبيق..."
        npm install
        if [ $? -ne 0 ]; then
            echo
            print_color $RED "❌ فشل في تثبيت المكتبات"
            print_color $YELLOW "🔧 يرجى التحقق من اتصال الإنترنت وإعادة المحاولة"
            echo
            read -p "اضغط Enter للمتابعة..."
            exit 1
        fi
        print_color $GREEN "✅ تم تثبيت المكتبات بنجاح"
    fi
    
    echo
    print_color $YELLOW "🎯 بدء تشغيل الخادم..."
    echo
    
    # Start the desktop application
    node desktop-launcher.js
    
    echo
    print_color $YELLOW "🛑 تم إيقاف التطبيق"
    echo
    read -p "اضغط Enter للمتابعة..."
}

# Make the script executable
chmod +x "$0"

# Run main function
main